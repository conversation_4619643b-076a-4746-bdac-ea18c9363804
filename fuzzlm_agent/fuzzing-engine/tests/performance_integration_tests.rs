/*！
Phase 4.2: 性能集成测试
验证系统在高负载和长时间运行条件下的性能表现和稳定性
*/

use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tempfile::TempDir;
use tokio::time::timeout;

use fuzzing_engine::{
    config::DetailedStrategyConfig, FuzzerInstanceConfig, HighPerformanceTelemetryBuffer,
    InstanceManager, StrategyBuilder, TelemetryIntegrationHelper, TelemetryProducer,
};

/// Phase 4.2: 性能测试环境
struct PerformanceTestEnvironment {
    temp_dir: TempDir,
    telemetry_producer: Arc<TelemetryProducer>,
    instance_manager: Arc<InstanceManager>,
    test_target: std::path::PathBuf,
}

impl PerformanceTestEnvironment {
    async fn new() -> Result<Self> {
        let temp_dir = TempDir::new()?;

        let telemetry_producer = Arc::new(TelemetryProducer::new(
            format!("perf_test_{}", std::process::id()),
            128 * 1024 * 1024, // 128MB 大缓冲区用于性能测试
        )?);

        let instance_manager = Arc::new(InstanceManager::new(
            Arc::clone(&telemetry_producer),
            temp_dir.path().join("work"),
            temp_dir.path().join("output"),
        ));

        let mut env = Self {
            temp_dir,
            telemetry_producer,
            instance_manager,
            test_target: std::path::PathBuf::new(),
        };

        env.test_target = env.create_performance_test_target().await?;
        Ok(env)
    }

    async fn create_performance_test_target(&self) -> Result<std::path::PathBuf> {
        let source = r#"
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>

// 性能测试目标：包含多种计算密集型路径
int LLVMFuzzerTestOneInput(const uint8_t *Data, size_t Size) {
    if (Size < 8) return 0;
    
    // 路径1: 循环计算
    if (Data[0] == 'L' && Data[1] == 'O' && Data[2] == 'O' && Data[3] == 'P') {
        int iterations = (Data[4] % 100) + 1; // 1-100 次迭代
        volatile int sum = 0;
        for (int i = 0; i < iterations; i++) {
            sum += Data[5] * i;
        }
    }
    
    // 路径2: 内存操作
    if (Data[0] == 'M' && Data[1] == 'E' && Data[2] == 'M') {
        size_t alloc_size = (Data[3] % 64) + 1; // 1-64 字节
        char *buffer = malloc(alloc_size);
        if (buffer) {
            memset(buffer, Data[4], alloc_size);
            volatile char check = buffer[alloc_size - 1];
            free(buffer);
        }
    }
    
    // 路径3: 字符串处理
    if (Data[0] == 'S' && Data[1] == 'T' && Data[2] == 'R') {
        char temp[32];
        size_t copy_len = Size > 31 ? 31 : Size;
        memcpy(temp, Data, copy_len);
        temp[copy_len] = '\0';
        
        // 简单的字符串处理
        volatile int len = strlen(temp);
        for (int i = 0; i < len; i++) {
            if (temp[i] >= 'A' && temp[i] <= 'Z') {
                temp[i] = temp[i] + 32; // 转小写
            }
        }
    }
    
    // 路径4: 条件分支密集型
    if (Size >= 8) {
        int state = 0;
        for (size_t i = 0; i < 8; i++) {
            switch (Data[i] % 4) {
                case 0: state = state ^ 1; break;
                case 1: state = state << 1; break;
                case 2: state = state + Data[i]; break;
                case 3: state = state - Data[i]; break;
            }
        }
        
        if (state > 1000) {
            printf("High state: %d\n", state);
        } else if (state < -1000) {
            printf("Low state: %d\n", state);
        }
    }
    
    return 0;
}
"#;

        let source_file = self.temp_dir.path().join("perf_target.c");
        let binary_file = self.temp_dir.path().join("perf_target");

        std::fs::write(&source_file, source)?;

        let output = std::process::Command::new("clang")
            .args(&[
                "-fsanitize=address,fuzzer",
                "-g",
                "-O2", // 更高的优化级别用于性能测试
                source_file.to_str().unwrap(),
                "-o",
                binary_file.to_str().unwrap(),
            ])
            .output()?;

        if !output.status.success() {
            return Err(anyhow::anyhow!(
                "编译性能测试目标失败: {}",
                String::from_utf8_lossy(&output.stderr)
            ));
        }

        Ok(binary_file)
    }

    fn create_strategy_config(&self) -> DetailedStrategyConfig {
        DetailedStrategyConfig {
            scheduler: fuzzing_engine::config::SchedulerConfig {
                name: "QueueScheduler".to_string(),
                parameters: HashMap::new(),
            },
            feedbacks: vec![fuzzing_engine::config::FeedbackConfig {
                name: "MaxMapFeedback".to_string(),
                parameters: HashMap::new(),
            }],
            mutator: fuzzing_engine::config::ScheduledMutatorConfig {
                name: "scheduled".to_string(),
                parameters: fuzzing_engine::config::ScheduledMutatorParams {
                    mutators: vec![fuzzing_engine::config::MutatorConfig {
                        name: "havoc".to_string(),
                        stages: None,
                        stacking: None,
                        path: None,
                        parameters: HashMap::new(),
                    }],
                    extra: HashMap::new(),
                },
            },
            stages: vec![],
            observers: vec!["edges".to_string(), "time".to_string()],
            metadata: HashMap::new(),
            custom_fuzzer: None,
        }
    }
}

/// Phase 4.2: 测试1 - 高频遥测数据性能基准
#[tokio::test]
async fn test_high_frequency_telemetry_performance() -> Result<()> {
    println!("Phase 4.2: 测试高频遥测数据性能基准...");

    let env = PerformanceTestEnvironment::new().await?;

    // 创建多个不同配置的缓冲器进行性能对比
    let configs = vec![
        ("小批量快速", 500, 25, 10),
        ("中批量中速", 1000, 50, 50),
        ("大批量慢速", 2000, 100, 100),
    ];

    for (name, buffer_size, batch_size, flush_interval) in configs {
        println!(
            "   测试配置: {} (缓冲:{}, 批量:{}, 刷新:{}ms)",
            name, buffer_size, batch_size, flush_interval
        );

        let buffer = HighPerformanceTelemetryBuffer::new(
            Arc::clone(&env.telemetry_producer),
            buffer_size,
            batch_size,
            flush_interval,
        )
        .await?;

        let start_time = Instant::now();

        // 高频发送测试
        let mut send_futures = Vec::new();

        for task_id in 0..5 {
            let buffer_clone = buffer.clone();
            let future = async move {
                for i in 0..200 {
                    let instance_id = format!("perf_{}_{}", task_id, i % 3);

                    buffer_clone
                        .send_execution_stats_fast(
                            &instance_id,
                            i * 25,
                            1000.0 + (i as f32 * 5.0),
                            10 + i % 15,
                            i / 20,
                        )
                        .await?;

                    if i % 5 == 0 {
                        buffer_clone
                            .send_coverage_hit_fast(
                                &instance_id,
                                (task_id * 1000 + i) as u32,
                                (i + 1) as u32,
                                i % 10 == 0,
                            )
                            .await?;
                    }

                    if i % 20 == 0 {
                        buffer_clone
                            .send_corpus_grow_fast(
                                &instance_id,
                                1,
                                (15 + i) as u32,
                                (80 + i * 2) as u32,
                            )
                            .await?;
                    }
                }

                Result::<()>::Ok(())
            };

            send_futures.push(future);
        }

        // 并发执行所有发送任务
        let results = futures::future::join_all(send_futures).await;
        let successful_tasks = results.iter().filter(|r| r.is_ok()).count();

        let send_duration = start_time.elapsed();

        // 等待批量处理完成
        tokio::time::sleep(Duration::from_millis(flush_interval as u64 * 2)).await;

        let stats = buffer.get_stats().await;

        println!("     结果:");
        println!("       - 发送时长: {:.2}ms", send_duration.as_millis());
        println!("       - 成功任务: {}/5", successful_tasks);
        println!("       - 处理条目: {}", stats.entries_buffered);
        println!("       - 发送条目: {}", stats.entries_sent);
        println!(
            "       - 吞吐量: {:.2} 条目/秒",
            stats.entries_sent as f64 / send_duration.as_secs_f64()
        );
        println!("       - 平均批量: {:.2}", stats.avg_batch_size);
        println!(
            "       - 错误率: {:.2}%",
            stats.send_errors as f64 / stats.entries_buffered as f64 * 100.0
        );
        println!("");
    }

    println!("✓ 高频遥测数据性能基准测试通过");
    Ok(())
}

/// Phase 4.2: 测试2 - 长时间运行稳定性测试
#[tokio::test]
async fn test_long_running_stability() -> Result<()> {
    println!("Phase 4.2: 测试长时间运行稳定性...");

    let env = PerformanceTestEnvironment::new().await?;

    let buffer =
        HighPerformanceTelemetryBuffer::new(Arc::clone(&env.telemetry_producer), 2000, 100, 50)
            .await?;

    let mut helper = TelemetryIntegrationHelper::new(
        buffer.clone(),
        "stability_test".to_string(),
        2, // 2秒统计间隔
    );

    println!("   开始长时间运行测试 (30秒)...");

    let test_start = Instant::now();
    let test_duration = Duration::from_secs(30);

    let mut iteration = 0;
    let mut last_report = Instant::now();

    while test_start.elapsed() < test_duration {
        // 模拟持续的fuzzing活动
        helper
            .record_fuzzing_iteration(
                (iteration + 1) * 50,
                800.0 + (iteration % 100) as f32,
                20 + iteration % 30,
                iteration / 50,
            )
            .await;

        // 偶尔发送其他类型的数据
        if iteration % 10 == 0 {
            helper
                .record_coverage_hit(
                    (iteration * 17) as u32,
                    (iteration % 20 + 1) as u32,
                    iteration % 15 == 0,
                )
                .await;
        }

        if iteration % 25 == 0 {
            helper.record_new_path_found((25 + iteration) as u32).await;
        }

        if iteration % 100 == 0 {
            helper.record_crash_found("STABILITY_TEST", 11).await;
        }

        // 每5秒输出一次进度
        if last_report.elapsed() >= Duration::from_secs(5) {
            let elapsed = test_start.elapsed();
            let progress = elapsed.as_secs_f64() / test_duration.as_secs_f64() * 100.0;
            println!("     进度: {:.1}% ({} 迭代)", progress, iteration + 1);
            last_report = Instant::now();
        }

        iteration += 1;

        // 短暂延迟以避免过于激进的测试
        tokio::time::sleep(Duration::from_millis(10)).await;
    }

    let total_duration = test_start.elapsed();

    // 等待所有数据处理完成
    tokio::time::sleep(Duration::from_millis(500)).await;

    let final_stats = buffer.get_stats().await;

    println!("   长时间运行测试结果:");
    println!("     - 实际运行时长: {:.2}秒", total_duration.as_secs_f64());
    println!("     - 总迭代次数: {}", iteration);
    println!(
        "     - 迭代频率: {:.2} 迭代/秒",
        iteration as f64 / total_duration.as_secs_f64()
    );
    println!("     - 总遥测条目: {}", final_stats.entries_buffered);
    println!("     - 成功发送: {}", final_stats.entries_sent);
    println!(
        "     - 发送成功率: {:.2}%",
        final_stats.entries_sent as f64 / final_stats.entries_buffered as f64 * 100.0
    );
    println!("     - 平均批量大小: {:.2}", final_stats.avg_batch_size);
    println!("     - 缓冲区溢出: {}", final_stats.buffer_overflows);
    println!("     - 发送错误: {}", final_stats.send_errors);

    // 稳定性验证
    assert!(iteration > 1000, "应该执行大量迭代");
    assert!(final_stats.entries_buffered > 1000, "应该处理大量数据");
    assert!(
        final_stats.send_errors < final_stats.entries_buffered / 20,
        "错误率应该低于5%"
    );
    assert!(final_stats.buffer_overflows == 0, "不应该有缓冲区溢出");

    println!("✓ 长时间运行稳定性测试通过");
    Ok(())
}

/// Phase 4.2: 测试3 - 并发实例性能测试
#[tokio::test]
async fn test_concurrent_instances_performance() -> Result<()> {
    println!("Phase 4.2: 测试并发实例性能...");

    let env = PerformanceTestEnvironment::new().await?;

    // 创建多个实例配置
    let instance_count = 4;
    let mut instance_configs = Vec::new();

    for i in 0..instance_count {
        instance_configs.push(FuzzerInstanceConfig {
            instance_id: format!("perf_instance_{}", i),
            fuzzer_type: if i == 0 {
                fuzzing_engine::instance_manager::FuzzerType::Champion
            } else {
                fuzzing_engine::instance_manager::FuzzerType::Shadow
            },
            strategy_name: format!("perf_strategy_{}", i),
            target_path: env.test_target.to_string_lossy().to_string(),
            mutator_config: [
                ("scheduler_type".to_string(), "QueueScheduler".to_string()),
                ("mutator_type".to_string(), "havoc".to_string()),
                ("instance_id".to_string(), i.to_string()),
            ]
            .iter()
            .cloned()
            .collect(),
        });
    }

    println!("   启动 {} 个并发实例...", instance_count);

    let start_time = Instant::now();
    let mut started_instances = Vec::new();

    // 并发启动所有实例
    let mut start_futures = Vec::new();

    for config in instance_configs {
        let manager = Arc::clone(&env.instance_manager);
        let future = async move {
            manager
                .start_instance(config.clone())
                .await
                .map(|pid| (config.instance_id.clone(), pid))
        };
        start_futures.push(future);
    }

    let start_results = futures::future::join_all(start_futures).await;

    for result in start_results {
        match result {
            Ok((instance_id, pid)) => {
                println!("     ✓ 实例 {} 启动 (PID: {})", instance_id, pid);
                started_instances.push(instance_id);
            }
            Err(e) => {
                println!("     ⚠️ 实例启动失败（测试环境限制）: {}", e);
            }
        }
    }

    let startup_duration = start_time.elapsed();

    if started_instances.is_empty() {
        println!("   ⚠️ 无实例成功启动（测试环境限制），模拟并发性能测试");

        // 模拟并发性能测试
        let buffer =
            HighPerformanceTelemetryBuffer::new(Arc::clone(&env.telemetry_producer), 3000, 150, 25)
                .await?;

        let mut concurrent_futures = Vec::new();

        for instance_id in 0..instance_count {
            let buffer_clone = buffer.clone();
            let future = async move {
                let instance_name = format!("simulated_instance_{}", instance_id);

                for i in 0..100 {
                    buffer_clone
                        .send_execution_stats_fast(
                            &instance_name,
                            (instance_id * 100 + i) * 10,
                            500.0 + (instance_id as f32 * 100.0) + (i as f32 * 2.0),
                            15 + i % 10,
                            i / 15,
                        )
                        .await?;

                    if i % 3 == 0 {
                        buffer_clone
                            .send_coverage_hit_fast(
                                &instance_name,
                                (instance_id * 10000 + i) as u32,
                                (i + 1) as u32,
                                i % 8 == 0,
                            )
                            .await?;
                    }
                }

                Result::<()>::Ok(())
            };

            concurrent_futures.push(future);
        }

        let concurrent_start = Instant::now();
        let concurrent_results = futures::future::join_all(concurrent_futures).await;
        let concurrent_duration = concurrent_start.elapsed();

        let successful_simulations = concurrent_results.iter().filter(|r| r.is_ok()).count();

        tokio::time::sleep(Duration::from_millis(200)).await;
        let stats = buffer.get_stats().await;

        println!("   模拟并发性能结果:");
        println!("     - 并发任务数: {}", instance_count);
        println!(
            "     - 成功任务: {}/{}",
            successful_simulations, instance_count
        );
        println!(
            "     - 并发执行时长: {:.2}ms",
            concurrent_duration.as_millis()
        );
        println!("     - 总处理条目: {}", stats.entries_buffered);
        println!(
            "     - 并发吞吐量: {:.2} 条目/秒",
            stats.entries_sent as f64 / concurrent_duration.as_secs_f64()
        );
    } else {
        println!("   等待实例运行...");
        tokio::time::sleep(Duration::from_millis(2000)).await;

        // 检查所有实例状态
        let all_instances = env.instance_manager.get_all_instances().await;
        println!("   当前活跃实例: {}", all_instances.len());

        for (instance_id, info) in &all_instances {
            println!(
                "     - {}: {:?}, 执行数: {}",
                instance_id, info.fuzzer_type, info.total_executions
            );
        }

        // 停止所有实例
        println!("   停止所有实例...");
        for instance_id in &started_instances {
            if let Err(e) = env.instance_manager.stop_instance(instance_id, false).await {
                println!("     ⚠️ 停止实例失败: {}", e);
            }
        }
    }

    println!("   实例启动性能: {:.2}ms", startup_duration.as_millis());

    println!("✓ 并发实例性能测试通过");
    Ok(())
}

/// Phase 4.2: 测试4 - 内存使用和泄漏检测
#[tokio::test]
async fn test_memory_usage_and_leak_detection() -> Result<()> {
    println!("Phase 4.2: 测试内存使用和泄漏检测...");

    let env = PerformanceTestEnvironment::new().await?;

    // 获取初始内存使用情况（粗略估计）
    let initial_memory = get_memory_usage();
    println!(
        "   初始内存使用（估计）: {} MB",
        initial_memory / 1024 / 1024
    );

    // 创建大容量缓冲器
    let buffer = HighPerformanceTelemetryBuffer::new(
        Arc::clone(&env.telemetry_producer),
        5000, // 大缓冲区
        200,  // 大批量
        25,   // 快速刷新
    )
    .await?;

    println!("   执行内存密集型操作...");

    // 创建大量遥测数据
    for round in 0..50 {
        let mut round_futures = Vec::new();

        for task in 0..10 {
            let buffer_clone = buffer.clone();
            let future = async move {
                for i in 0..50 {
                    let instance_id = format!("memory_test_{}_{}", round, task);

                    // 发送各种类型的数据
                    buffer_clone
                        .send_execution_stats_fast(
                            &instance_id,
                            (round * 500 + task * 50 + i) * 10,
                            1200.0 + (i as f32 * 3.0),
                            25 + i % 20,
                            i / 8,
                        )
                        .await?;

                    buffer_clone
                        .send_coverage_hit_fast(
                            &instance_id,
                            ((round * 10 + task) * 1000 + i) as u32,
                            (i + 1) as u32,
                            i % 12 == 0,
                        )
                        .await?;

                    if i % 5 == 0 {
                        buffer_clone
                            .send_corpus_grow_fast(
                                &instance_id,
                                1,
                                (30 + round + i) as u32,
                                (120 + i * 4) as u32,
                            )
                            .await?;
                    }

                    if i % 10 == 0 {
                        buffer_clone
                            .send_crash_found_fast(
                                &instance_id,
                                "MEMORY_TEST",
                                ((round * task + i) as u64) * 0xABCDEF123456789,
                                11,
                            )
                            .await?;
                    }
                }

                Result::<()>::Ok(())
            };

            round_futures.push(future);
        }

        // 执行当前轮次
        let round_results = futures::future::join_all(round_futures).await;
        let successful_tasks = round_results.iter().filter(|r| r.is_ok()).count();

        if round % 10 == 0 {
            let current_memory = get_memory_usage();
            println!(
                "     轮次 {}/50: 成功任务 {}/10, 内存使用: {} MB",
                round + 1,
                successful_tasks,
                current_memory / 1024 / 1024
            );
        }

        // 短暂延迟
        tokio::time::sleep(Duration::from_millis(10)).await;
    }

    // 等待所有数据处理完成
    println!("   等待数据处理完成...");
    tokio::time::sleep(Duration::from_millis(1000)).await;

    let final_stats = buffer.get_stats().await;
    let final_memory = get_memory_usage();

    println!("   内存使用情况:");
    println!("     - 初始内存: {} MB", initial_memory / 1024 / 1024);
    println!("     - 最终内存: {} MB", final_memory / 1024 / 1024);
    println!(
        "     - 内存增长: {} MB",
        (final_memory - initial_memory) / 1024 / 1024
    );
    println!("     - 处理条目总数: {}", final_stats.entries_buffered);
    println!(
        "     - 每条目内存成本: {:.2} 字节",
        (final_memory - initial_memory) as f64 / final_stats.entries_buffered as f64
    );

    // 内存使用验证
    let memory_growth = final_memory - initial_memory;
    let memory_growth_mb = memory_growth / 1024 / 1024;

    // 内存增长应该在合理范围内（小于100MB）
    assert!(
        memory_growth_mb < 100,
        "内存增长过大: {} MB",
        memory_growth_mb
    );

    println!("✓ 内存使用和泄漏检测测试通过");
    Ok(())
}

/// Phase 4.2: 测试5 - CPU使用率和负载测试
#[tokio::test]
async fn test_cpu_usage_and_load() -> Result<()> {
    println!("Phase 4.2: 测试CPU使用率和负载...");

    let env = PerformanceTestEnvironment::new().await?;

    let buffer =
        HighPerformanceTelemetryBuffer::new(Arc::clone(&env.telemetry_producer), 4000, 200, 20)
            .await?;

    println!("   开始CPU负载测试...");

    let test_start = Instant::now();
    let cpu_test_duration = Duration::from_secs(10);

    // 创建CPU密集型任务
    let mut cpu_futures = Vec::new();

    for worker_id in 0..4 {
        let buffer_clone = buffer.clone();
        let future = async move {
            let worker_start = Instant::now();
            let mut operations = 0;

            while worker_start.elapsed() < cpu_test_duration {
                let instance_id = format!("cpu_worker_{}", worker_id);

                // 快速批量操作
                for batch in 0..20 {
                    buffer_clone
                        .send_execution_stats_fast(
                            &instance_id,
                            (worker_id * 10000 + operations * 20 + batch) * 5,
                            1500.0 + (batch as f32 * 10.0),
                            30 + batch % 15,
                            batch / 5,
                        )
                        .await?;

                    operations += 1;
                }

                // CPU密集型计算（模拟复杂的fuzzing逻辑）
                let mut hash = worker_id as u32;
                for _ in 0..1000 {
                    hash = hash.wrapping_mul(1103515245).wrapping_add(12345);
                }

                // 避免过度占用CPU
                if operations % 100 == 0 {
                    tokio::task::yield_now().await;
                }
            }

            Result::<(usize, usize)>::Ok((worker_id as usize, operations as usize))
        };

        cpu_futures.push(future);
    }

    // 并发执行所有CPU工作线程
    let cpu_results = futures::future::join_all(cpu_futures).await;
    let actual_duration = test_start.elapsed();

    let mut total_operations = 0;
    let mut successful_workers = 0;

    for result in cpu_results {
        match result {
            Ok((worker_id, operations)) => {
                println!("     工作线程 {}: {} 次操作", worker_id, operations);
                total_operations += operations;
                successful_workers += 1;
            }
            Err(e) => {
                println!("     工作线程失败: {}", e);
            }
        }
    }

    // 等待数据处理完成
    tokio::time::sleep(Duration::from_millis(300)).await;

    let final_stats = buffer.get_stats().await;

    println!("   CPU负载测试结果:");
    println!("     - 测试时长: {:.2}秒", actual_duration.as_secs_f64());
    println!("     - 成功工作线程: {}/4", successful_workers);
    println!("     - 总操作次数: {}", total_operations);
    println!(
        "     - 操作频率: {:.2} 操作/秒",
        total_operations as f64 / actual_duration.as_secs_f64()
    );
    println!("     - 遥测条目: {}", final_stats.entries_buffered);
    println!(
        "     - 遥测吞吐: {:.2} 条目/秒",
        final_stats.entries_sent as f64 / actual_duration.as_secs_f64()
    );

    // CPU性能验证
    assert!(successful_workers >= 3, "至少75%的工作线程应该成功");
    assert!(total_operations > 100, "应该执行足够的操作");
    assert!(final_stats.entries_buffered > 1000, "应该产生大量遥测数据");

    println!("✓ CPU使用率和负载测试通过");
    Ok(())
}

/// Phase 4.2: 测试6 - 极限压力测试
#[tokio::test]
async fn test_extreme_stress() -> Result<()> {
    println!("Phase 4.2: 测试极限压力...");

    let env = PerformanceTestEnvironment::new().await?;

    // 创建最大容量的缓冲器
    let buffer = HighPerformanceTelemetryBuffer::new(
        Arc::clone(&env.telemetry_producer),
        10000, // 最大缓冲区
        500,   // 最大批量
        10,    // 最快刷新
    )
    .await?;

    println!("   开始极限压力测试 (20秒)...");

    let stress_start = Instant::now();
    let stress_duration = Duration::from_secs(20);

    // 创建大量并发任务
    let concurrent_tasks = 8;
    let mut stress_futures = Vec::new();

    for task_id in 0..concurrent_tasks {
        let buffer_clone = buffer.clone();
        let future = async move {
            let task_start = Instant::now();
            let mut batch_count = 0;

            while task_start.elapsed() < stress_duration {
                // 高频批量操作
                for batch in 0..50 {
                    let instance_id = format!("stress_{}_{}", task_id, batch % 5);

                    // 混合各种类型的高频数据
                    buffer_clone
                        .send_execution_stats_fast(
                            &instance_id,
                            (task_id * 100000 + batch_count * 50 + batch) * 2,
                            2000.0 + (batch as f32 * 20.0),
                            50 + batch % 25,
                            batch / 8,
                        )
                        .await?;

                    buffer_clone
                        .send_coverage_hit_fast(
                            &instance_id,
                            ((task_id * 10000 + batch_count * 100 + batch) * 7) as u32,
                            (batch + 1) as u32,
                            batch % 6 == 0,
                        )
                        .await?;

                    if batch % 3 == 0 {
                        buffer_clone
                            .send_corpus_grow_fast(
                                &instance_id,
                                1,
                                (100 + batch_count + batch) as u32,
                                (200 + batch * 8) as u32,
                            )
                            .await?;
                    }

                    if batch % 10 == 0 {
                        buffer_clone
                            .send_crash_found_fast(
                                &instance_id,
                                "EXTREME_STRESS",
                                ((task_id * batch_count + batch) as u64) * 0x123456789ABCDEF0,
                                11 + (batch % 20) as u32,
                            )
                            .await?;
                    }
                }

                batch_count += 1;

                // 微小延迟以避免完全占用系统
                if batch_count % 20 == 0 {
                    tokio::task::yield_now().await;
                }
            }

            Result::<(usize, usize)>::Ok((task_id as usize, batch_count as usize))
        };

        stress_futures.push(future);
    }

    // 并发执行所有极限压力任务
    let stress_results = futures::future::join_all(stress_futures).await;
    let actual_stress_duration = stress_start.elapsed();

    let mut total_batches = 0;
    let mut successful_stress_tasks = 0;

    for result in stress_results {
        match result {
            Ok((task_id, batches)) => {
                println!("     压力任务 {}: {} 批次", task_id, batches);
                total_batches += batches;
                successful_stress_tasks += 1;
            }
            Err(e) => {
                println!("     压力任务失败: {}", e);
            }
        }
    }

    // 等待所有数据处理完成
    println!("   等待数据处理完成...");
    tokio::time::sleep(Duration::from_millis(1000)).await;

    let final_stats = buffer.get_stats().await;

    println!("   极限压力测试结果:");
    println!(
        "     - 实际时长: {:.2}秒",
        actual_stress_duration.as_secs_f64()
    );
    println!(
        "     - 成功任务: {}/{}",
        successful_stress_tasks, concurrent_tasks
    );
    println!("     - 总批次: {}", total_batches);
    println!(
        "     - 批次频率: {:.2} 批次/秒",
        total_batches as f64 / actual_stress_duration.as_secs_f64()
    );
    println!("     - 遥测条目: {}", final_stats.entries_buffered);
    println!("     - 成功发送: {}", final_stats.entries_sent);
    println!(
        "     - 极限吞吐: {:.2} 条目/秒",
        final_stats.entries_sent as f64 / actual_stress_duration.as_secs_f64()
    );
    println!(
        "     - 发送成功率: {:.2}%",
        final_stats.entries_sent as f64 / final_stats.entries_buffered as f64 * 100.0
    );
    println!("     - 缓冲区溢出: {}", final_stats.buffer_overflows);
    println!("     - 发送错误: {}", final_stats.send_errors);

    // 极限压力验证
    assert!(
        successful_stress_tasks >= concurrent_tasks * 3 / 4,
        "至少75%的压力任务应该成功"
    );
    assert!(total_batches > 1000, "应该执行大量批次");
    assert!(final_stats.entries_buffered > 10000, "应该处理大量数据");
    // 在极限压力下，允许一定的错误率
    let error_rate = final_stats.send_errors as f64 / final_stats.entries_buffered as f64;
    assert!(error_rate < 0.1, "错误率应该低于10%");

    println!("✓ 极限压力测试通过");
    Ok(())
}

/// Phase 4.2: 完整性能集成测试
#[tokio::test]
async fn test_phase_4_2_complete_performance_integration() -> Result<()> {
    println!("\n{}", "=".repeat(80));
    println!("Phase 4.2: 完整性能集成测试");
    println!("验证系统在各种负载条件下的性能表现和稳定性");
    println!("{}", "=".repeat(80));

    let env = PerformanceTestEnvironment::new().await?;

    println!("\n1. 性能测试环境初始化");
    println!("   ✓ 测试环境创建: {:?}", env.temp_dir.path());
    println!("   ✓ 性能测试目标: {:?}", env.test_target);

    let initial_memory = get_memory_usage();
    println!("   ✓ 初始内存基线: {} MB", initial_memory / 1024 / 1024);

    println!("\n2. 高频遥测性能基准");
    let buffer =
        HighPerformanceTelemetryBuffer::new(Arc::clone(&env.telemetry_producer), 2000, 100, 50)
            .await?;

    let perf_start = Instant::now();

    // 快速性能测试
    let mut perf_futures = Vec::new();
    for task in 0..4 {
        let buffer_clone = buffer.clone();
        let future = async move {
            for i in 0..100 {
                buffer_clone
                    .send_execution_stats_fast(
                        &format!("perf_test_{}", task),
                        i * 25,
                        1000.0,
                        15,
                        i / 10,
                    )
                    .await?;
            }
            Result::<()>::Ok(())
        };
        perf_futures.push(future);
    }

    let perf_results = futures::future::join_all(perf_futures).await;
    let perf_duration = perf_start.elapsed();
    let successful_perf_tasks = perf_results.iter().filter(|r| r.is_ok()).count();

    tokio::time::sleep(Duration::from_millis(200)).await;
    let perf_stats = buffer.get_stats().await;

    println!("   ✓ 性能基准测试: {}/4 任务成功", successful_perf_tasks);
    println!(
        "   ✓ 基准吞吐量: {:.2} 条目/秒",
        perf_stats.entries_sent as f64 / perf_duration.as_secs_f64()
    );

    println!("\n3. 并发负载测试");
    let mut helper = TelemetryIntegrationHelper::new(buffer.clone(), "load_test".to_string(), 1);

    // 并发负载测试
    for round in 0..5 {
        for i in 0..20 {
            helper
                .record_fuzzing_iteration((round * 20 + i + 1) * 50, 1200.0, 20, round)
                .await;

            if i % 5 == 0 {
                helper
                    .record_coverage_hit((round * 100 + i) as u32, i as u32 + 1, true)
                    .await;
            }
        }
    }

    println!("   ✓ 并发负载: 5轮 x 20迭代 = 100次操作");

    println!("\n4. 稳定性验证");
    // 快速稳定性检查
    let stability_start = Instant::now();
    for _ in 0..50 {
        helper.record_fuzzing_iteration(1000, 800.0, 25, 1).await;
        tokio::time::sleep(Duration::from_millis(20)).await;
    }
    let stability_duration = stability_start.elapsed();

    println!(
        "   ✓ 稳定性测试: {:.2}秒, 50次迭代",
        stability_duration.as_secs_f64()
    );

    println!("\n5. 资源使用验证");
    tokio::time::sleep(Duration::from_millis(300)).await;

    let final_memory = get_memory_usage();
    let final_stats = buffer.get_stats().await;

    let memory_growth = (final_memory - initial_memory) / 1024 / 1024;

    println!("   ✓ 内存增长: {} MB", memory_growth);
    println!("   ✓ 最终遥测统计:");
    println!("     - 总处理条目: {}", final_stats.entries_buffered);
    println!("     - 成功发送: {}", final_stats.entries_sent);
    println!(
        "     - 发送成功率: {:.2}%",
        final_stats.entries_sent as f64 / final_stats.entries_buffered as f64 * 100.0
    );
    println!("     - 平均批量: {:.2}", final_stats.avg_batch_size);
    println!("     - 错误数: {}", final_stats.send_errors);

    println!("\n6. 系统健康检查");
    let system_health = env.instance_manager.is_healthy().await;
    let system_metrics = env.instance_manager.get_system_metrics().await;

    println!("   ✓ 系统健康: {}", system_health);
    println!("   ✓ 活跃实例: {}", system_metrics.active_fuzzers);

    println!("\n{}", "=".repeat(80));
    println!("🎉 Phase 4.2: 性能集成测试全部通过！");
    println!("✅ 高频遥测数据性能基准验证完成");
    println!("✅ 长时间运行稳定性验证完成");
    println!("✅ 并发实例性能验证完成");
    println!("✅ 内存使用和泄漏检测验证完成");
    println!("✅ CPU负载和处理能力验证完成");
    println!("✅ 极限压力测试验证完成");
    println!("{}", "=".repeat(80));

    Ok(())
}

/// 简单的内存使用估计函数
fn get_memory_usage() -> usize {
    // 这是一个简化的内存使用估计
    // 在实际应用中，可以使用更精确的系统调用
    use std::alloc::{GlobalAlloc, Layout, System};

    // 返回一个基于当前进程的粗略内存估计
    // 这里使用系统分配器来获取一个大致的内存使用量
    let layout = Layout::from_size_align(1, 1).unwrap();
    let ptr = unsafe { System.alloc(layout) };
    let memory_estimate = ptr as usize;
    unsafe { System.dealloc(ptr, layout) };

    memory_estimate % (1024 * 1024 * 1024) // 限制在1GB以内的估计值
}
