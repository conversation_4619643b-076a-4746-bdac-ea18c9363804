[package]
name = "fuzzing-engine"
version = "0.1.0"
edition = "2021"

[dependencies]
# LibAFL 核心依赖
libafl = { path = "../../LibAFL/libafl" }
libafl_bolts = { path = "../../LibAFL/libafl_bolts" }
libafl_targets = { path = "../../LibAFL/libafl_targets", features = ["coverage","sancov_pcguard_hitcounts", "sancov_cmplog", "libfuzzer"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# gRPC 通信 (新增)
tonic = "0.10"
prost = "0.12"
tonic-build = "0.10"

# 共享内存 (新增)
memmap2 = "0.9"
byteorder = "1.5"


# JSON 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 二进制序列化 (用于状态快照)
bincode = "1.3"

# 压缩支持 (用于状态传输)
flate2 = "1.0"

# 命令行参数解析
clap = { version = "4.0", features = ["derive"] }

# UUID 生成 (新增)
uuid = { version = "1.6", features = ["v4"] }

# 临时文件管理 (新增)
tempfile = "3.8"

# 动态库加载 (新增)
libloading = "0.8"

# 随机数生成 (用于测试种子)
rand = "0.8"

# 异步编程工具 (用于测试)
futures = "0.3"



# 日志
log = "0.4"
env_logger = "0.10"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 内存分配器
mimalloc = { version = "0.1", default-features = false }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 系统信息
sysinfo = "0.30"

# Unix 系统调用 (仅 Unix 平台)
[target.'cfg(unix)'.dependencies]
nix = { version = "0.27", features = ["process", "signal"] }

[build-dependencies]
tonic-build = "0.10"
prost-build = "0.12"
cc = { version = "1.1.21", features = ["parallel"] }
pkg-config = "0.3"

[dev-dependencies]
tokio-test = "0.4"

[lib]
name = "fuzzing_engine"
crate-type = ["cdylib", "rlib"]

[[bin]]
name = "fuzzing-engine"
path = "src/main.rs"

[[bin]]
name = "libafl-harness"
path = "src/harnesses/libafl_harness.rs"



