/*!
遥测数据生产者
负责将 Fuzzer 实例的原始性能数据写入共享内存
*/

use anyhow::{anyhow, Result};
use byteorder::{ByteOrder, LittleEndian};
use log::{debug, error, info};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex;

/// 遥测数据类型（与 Python 端保持一致）
#[repr(u32)]
#[derive(Debug, Clone, Copy)]
pub enum TelemetryDataType {
    ExecutionCount = 1,
    CoverageHit = 2,
    CrashFound = 3,
    QueueUpdate = 4,
    EnergyUpdate = 5,
    CorpusGrow = 6,
    HangFound = 7,
    MutatorStats = 8,
    SchedulerStats = 9,
    FeedbackScore = 10,
}

/// 遥测条目
#[derive(Debug, Clone)]
pub struct TelemetryEntry {
    pub data_type: TelemetryDataType,
    pub instance_id: String,
    pub timestamp_ns: u64,
    pub data: Vec<u8>,
}

impl TelemetryEntry {
    /// 转换为字节格式
    pub fn to_bytes(&self) -> Vec<u8> {
        let instance_id_hash = self.hash_instance_id(&self.instance_id);
        let mut bytes = Vec::new();

        // 条目头：类型(4) + 实例ID哈希(4) + 时间戳(8) + 数据大小(4) = 20字节
        bytes.extend_from_slice(&(self.data_type as u32).to_le_bytes());
        bytes.extend_from_slice(&instance_id_hash.to_le_bytes());
        bytes.extend_from_slice(&self.timestamp_ns.to_le_bytes());
        bytes.extend_from_slice(&(self.data.len() as u32).to_le_bytes());

        // 数据部分
        bytes.extend_from_slice(&self.data);

        bytes
    }

    fn hash_instance_id(&self, instance_id: &str) -> u32 {
        // 简单的字符串哈希
        let mut hash = 0u32;
        for byte in instance_id.bytes() {
            hash = hash.wrapping_mul(31).wrapping_add(byte as u32);
        }
        hash
    }
}

/// 共享内存环形缓冲区（Rust 端 - 生产者）
pub struct SharedMemoryRing {
    memory: Arc<Mutex<memmap2::MmapMut>>,
    metadata_size: usize,
    data_size: usize,
    write_pos: Arc<Mutex<usize>>,
    sequence_number: Arc<Mutex<u64>>,
}

impl SharedMemoryRing {
    /// 创建共享内存环形缓冲区（生产者模式）
    pub async fn create_producer(name: &str, size: usize) -> Result<Self> {
        use memmap2::MmapOptions;
        use std::fs::OpenOptions;
        use std::os::unix::fs::OpenOptionsExt;

        // 支持通过环境变量配置共享内存路径，默认使用runtime/temp
        let base_path = std::env::var("FUZZLM_SHM_DIR").unwrap_or_else(|_| {
            // 尝试使用项目根目录下的runtime/temp
            let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
            let mut runtime_path = current_dir;
            // 如果在fuzzing-engine目录中，回到项目根目录
            if runtime_path.ends_with("fuzzing-engine") {
                runtime_path.pop();
                runtime_path.pop();
            }
            runtime_path
                .join("runtime")
                .join("temp")
                .to_string_lossy()
                .to_string()
        });

        let shm_path = format!("{base_path}/fuzzlm_telemetry_{name}");

        // 确保目录存在
        if let Some(parent) = Path::new(&shm_path).parent() {
            std::fs::create_dir_all(parent)?;
        }

        // 创建共享内存文件
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .mode(0o666)
            .open(&shm_path)?;

        // 设置文件大小
        file.set_len(size as u64)?;

        // 内存映射
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };

        let metadata_size = 64; // 元数据区域大小
        let data_size = size - metadata_size;

        let ring = Self {
            memory: Arc::new(Mutex::new(mmap)),
            metadata_size,
            data_size,
            write_pos: Arc::new(Mutex::new(0)),
            sequence_number: Arc::new(Mutex::new(0)),
        };

        // 初始化元数据
        ring.write_metadata(0, 0).await?;

        info!(
            "✓ 共享内存环形缓冲区创建成功: {} ({}KB)",
            shm_path,
            size / 1024
        );

        Ok(ring)
    }

    /// 写入元数据
    async fn write_metadata(&self, write_pos: usize, seq_num: u64) -> Result<()> {
        let mut memory = self.memory.lock().await;

        let timestamp_ns = SystemTime::now().duration_since(UNIX_EPOCH)?.as_nanos() as u64;

        // 格式: magic(4) + write_pos(8) + seq_num(8) + timestamp(8) + reserved(36)
        let magic = 0x54454C45u32; // "TELE"

        LittleEndian::write_u32(&mut memory[0..4], magic);
        LittleEndian::write_u64(&mut memory[4..12], write_pos as u64);
        LittleEndian::write_u64(&mut memory[12..20], seq_num);
        LittleEndian::write_u64(&mut memory[20..28], timestamp_ns);
        // 保留区域 28..64 清零
        for i in 28..64 {
            memory[i] = 0;
        }

        Ok(())
    }

    /// 写入遥测条目
    pub async fn write_entry(&self, entry: &TelemetryEntry) -> Result<()> {
        let entry_bytes = entry.to_bytes();
        let entry_size = entry_bytes.len();

        if entry_size > self.data_size {
            return Err(anyhow!("条目太大: {} > {}", entry_size, self.data_size));
        }

        let mut memory = self.memory.lock().await;
        let mut write_pos = self.write_pos.lock().await;
        let mut seq_num = self.sequence_number.lock().await;

        let current_pos = *write_pos;
        let new_pos = (current_pos + entry_size) % self.data_size;

        // 写入数据
        let data_offset = self.metadata_size;

        if current_pos + entry_size <= self.data_size {
            // 数据不会回绕
            let start = data_offset + current_pos;
            memory[start..start + entry_size].copy_from_slice(&entry_bytes);
        } else {
            // 数据需要回绕
            let first_part_size = self.data_size - current_pos;
            let second_part_size = entry_size - first_part_size;

            // 写入第一部分
            let start = data_offset + current_pos;
            memory[start..start + first_part_size].copy_from_slice(&entry_bytes[..first_part_size]);

            // 写入第二部分
            memory[data_offset..data_offset + second_part_size]
                .copy_from_slice(&entry_bytes[first_part_size..]);
        }

        // 更新位置和序列号
        *write_pos = new_pos;
        *seq_num += 1;

        // 释放锁后更新元数据
        drop(memory);
        drop(write_pos);
        drop(seq_num);

        self.write_metadata(new_pos, *self.sequence_number.lock().await)
            .await?;

        debug!(
            "遥测条目已写入: {:?}, 大小: {}",
            entry.data_type, entry_size
        );

        Ok(())
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> (usize, u64) {
        let write_pos = *self.write_pos.lock().await;
        let seq_num = *self.sequence_number.lock().await;
        (write_pos, seq_num)
    }
}

/// 遥测数据生产者
pub struct TelemetryProducer {
    ring_buffer: Arc<SharedMemoryRing>,
    stats: Arc<Mutex<ProducerStats>>,
}

#[derive(Debug, Default)]
pub struct ProducerStats {
    entries_written: u64,
    bytes_written: u64,
    errors: u64,
    last_write_time: Option<SystemTime>,
}

impl TelemetryProducer {
    /// 创建遥测生产者
    pub async fn new(stream_name: &str, buffer_size: usize) -> Result<Self> {
        let ring_buffer = SharedMemoryRing::create_producer(stream_name, buffer_size).await?;

        Ok(Self {
            ring_buffer: Arc::new(ring_buffer),
            stats: Arc::new(Mutex::new(ProducerStats::default())),
        })
    }

    /// 发送执行统计数据
    pub async fn send_execution_stats(
        &self,
        instance_id: String,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    ) {
        // Phase 3.1: 添加遥测数据验证逻辑
        // 验证1: 空corpus时执行速度不应过高
        if corpus_size == 0 && exec_per_sec > 10.0 {
            debug!(
                "遥测数据验证失败: 空corpus时执行速度过高 (corpus={corpus_size}, exec/s={exec_per_sec})"
            );
            // 修正不合理的执行速度
            let corrected_exec_per_sec = 0.0_f32;
            
            // 构造修正后的数据
            let mut data = Vec::with_capacity(16);
            data.extend_from_slice(&executions.to_le_bytes());
            data.extend_from_slice(&corrected_exec_per_sec.to_le_bytes());
            data.extend_from_slice(&corpus_size.to_le_bytes());
            data.extend_from_slice(&crashes.to_le_bytes());

            let entry = TelemetryEntry {
                data_type: TelemetryDataType::ExecutionCount,
                instance_id,
                timestamp_ns: SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_nanos() as u64,
                data,
            };

            if let Err(e) = self.write_entry(entry).await {
                error!("发送执行统计失败: {e}");
            }
            return;
        }
        
        // 验证2: 执行速度合理性检查
        if exec_per_sec > 1_000_000.0 {
            debug!(
                "遥测数据验证警告: 执行速度异常高 (exec/s={exec_per_sec}), 限制到100万/秒"
            );
            let capped_exec_per_sec = 1_000_000.0_f32;
            
            // 构造限制后的数据
            let mut data = Vec::with_capacity(16);
            data.extend_from_slice(&executions.to_le_bytes());
            data.extend_from_slice(&capped_exec_per_sec.to_le_bytes());
            data.extend_from_slice(&corpus_size.to_le_bytes());
            data.extend_from_slice(&crashes.to_le_bytes());

            let entry = TelemetryEntry {
                data_type: TelemetryDataType::ExecutionCount,
                instance_id,
                timestamp_ns: SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_nanos() as u64,
                data,
            };

            if let Err(e) = self.write_entry(entry).await {
                error!("发送执行统计失败: {e}");
            }
            return;
        }
        
        // 验证3: crashes不应超过executions
        let validated_crashes = if crashes > executions {
            debug!(
                "遥测数据验证警告: 崩溃数超过执行数 (crashes={crashes}, executions={executions})"
            );
            executions
        } else {
            crashes
        };

        // 构造数据：executions(4) + exec_per_sec(4) + corpus_size(4) + crashes(4)
        let mut data = Vec::with_capacity(16);
        data.extend_from_slice(&executions.to_le_bytes());
        data.extend_from_slice(&exec_per_sec.to_le_bytes());
        data.extend_from_slice(&corpus_size.to_le_bytes());
        data.extend_from_slice(&validated_crashes.to_le_bytes());

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::ExecutionCount,
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送执行统计失败: {e}");
        }
    }

    /// 发送覆盖率命中数据
    pub async fn send_coverage_hit(
        &self,
        instance_id: String,
        edge_id: u32,
        hit_count: u32,
        is_new: bool,
    ) {
        let mut data = Vec::with_capacity(9);
        data.extend_from_slice(&edge_id.to_le_bytes());
        data.extend_from_slice(&hit_count.to_le_bytes());
        data.push(if is_new { 1 } else { 0 });

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CoverageHit,
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送覆盖率命中失败: {e}");
        }
    }

    /// 发送崩溃发现数据
    pub async fn send_crash_found(
        &self,
        instance_id: String,
        crash_type: String,
        input_hash: u64,
        signal: u32,
    ) {
        let crash_type_bytes = crash_type.into_bytes();
        let crash_type_len = crash_type_bytes.len() as u32;

        let mut data = Vec::with_capacity(16 + crash_type_bytes.len());
        data.extend_from_slice(&crash_type_len.to_le_bytes());
        data.extend_from_slice(&input_hash.to_le_bytes());
        data.extend_from_slice(&signal.to_le_bytes());
        data.extend_from_slice(&crash_type_bytes);

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CrashFound,
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送崩溃发现失败: {e}");
        }
    }

    /// 发送语料库增长数据
    pub async fn send_corpus_grow(
        &self,
        instance_id: String,
        new_inputs: u32,
        total_size: u32,
        avg_length: u32,
    ) {
        let mut data = Vec::with_capacity(12);
        data.extend_from_slice(&new_inputs.to_le_bytes());
        data.extend_from_slice(&total_size.to_le_bytes());
        data.extend_from_slice(&avg_length.to_le_bytes());

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CorpusGrow,
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送语料库增长失败: {e}");
        }
    }

    /// 发送变异器统计数据
    pub async fn send_mutator_stats(
        &self,
        instance_id: String,
        mutator_id: u32,
        usage_count: u32,
        success_rate: f32,
    ) {
        let mut data = Vec::with_capacity(12);
        data.extend_from_slice(&mutator_id.to_le_bytes());
        data.extend_from_slice(&usage_count.to_le_bytes());
        data.extend_from_slice(&success_rate.to_le_bytes());

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::MutatorStats,
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送变异器统计失败: {e}");
        }
    }

    /// 写入遥测条目
    async fn write_entry(&self, entry: TelemetryEntry) -> Result<()> {
        let result = self.ring_buffer.write_entry(&entry).await;

        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;

            match &result {
                Ok(_) => {
                    stats.entries_written += 1;
                    stats.bytes_written += entry.data.len() as u64;
                    stats.last_write_time = Some(SystemTime::now());
                }
                Err(_) => {
                    stats.errors += 1;
                }
            }
        }

        result
    }

    /// 获取生产者统计信息
    pub async fn get_stats(&self) -> ProducerStats {
        self.stats.lock().await.clone()
    }

    /// 获取缓冲区统计信息
    pub async fn get_buffer_stats(&self) -> (usize, u64) {
        self.ring_buffer.get_stats().await
    }
}

impl Clone for ProducerStats {
    fn clone(&self) -> Self {
        Self {
            entries_written: self.entries_written,
            bytes_written: self.bytes_written,
            errors: self.errors,
            last_write_time: self.last_write_time,
        }
    }
}
