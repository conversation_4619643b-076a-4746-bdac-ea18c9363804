/*!
LibAFL Harness启动器
根据策略配置动态选择和启动合适的LibAFL harness
*/

use anyhow::{anyhow, Result};
use log::{error, info, warn};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::process::{Command, Stdio};
use tokio::io::AsyncWriteExt;

use crate::config::{CustomFuzzerConfig, DetailedStrategyConfig};
use crate::path_resolver::PathResolver;

/// Harness类型映射
#[derive(Debug)]
pub struct HarnessInfo {
    pub binary_name: String,
    pub description: String,
    pub supported_mutators: Vec<String>,
    pub supported_schedulers: Vec<String>,
}

/// LibAFL Harness启动器
pub struct HarnessLauncher {
    harness_map: HashMap<String, HarnessInfo>,
    fuzzbench_dir: PathBuf,
    /// 动态编译的fuzzer缓存
    compiled_fuzzers: std::sync::Mutex<HashMap<String, PathBuf>>,
    /// 路径解析器
    path_resolver: PathResolver,
}

impl Default for HarnessLauncher {
    fn default() -> Self {
        Self::new()
    }
}

impl HarnessLauncher {
    /// 创建新的启动器
    pub fn new() -> Self {
        let mut harness_map = HashMap::new();

        // 注册可用的harness
        harness_map.insert(
            "fast".to_string(),
            HarnessInfo {
                binary_name: "fast_cc".to_string(),
                description: "Fast fuzzer with basic coverage feedback".to_string(),
                supported_mutators: vec!["havoc".to_string()],
                supported_schedulers: vec!["queue".to_string(), "weighted".to_string()],
            },
        );

        harness_map.insert(
            "mopt".to_string(),
            HarnessInfo {
                binary_name: "mopt_cc".to_string(),
                description: "MOpt-based mutational fuzzing".to_string(),
                supported_mutators: vec!["mopt".to_string()],
                supported_schedulers: vec!["queue".to_string()],
            },
        );

        harness_map.insert(
            "cmplog".to_string(),
            HarnessInfo {
                binary_name: "cmplog_cc".to_string(),
                description: "CmpLog-based constraint solving".to_string(),
                supported_mutators: vec!["havoc".to_string(), "tokens".to_string()],
                supported_schedulers: vec!["queue".to_string()],
            },
        );

        harness_map.insert(
            "generic".to_string(),
            HarnessInfo {
                binary_name: "generic_cc".to_string(),
                description: "Generic fuzzer with configurable components".to_string(),
                supported_mutators: vec!["havoc".to_string(), "tokens".to_string()],
                supported_schedulers: vec![
                    "queue".to_string(),
                    "weighted".to_string(),
                    "power".to_string(),
                ],
            },
        );

        harness_map.insert(
            "explore".to_string(),
            HarnessInfo {
                binary_name: "explore_cc".to_string(),
                description: "Exploration-focused fuzzer".to_string(),
                supported_mutators: vec!["havoc".to_string()],
                supported_schedulers: vec!["explore".to_string()],
            },
        );

        // 创建路径解析器
        let path_resolver = PathResolver::with_cache();

        // 查找fuzzbench目录
        let fuzzbench_dir = PathResolver::find_fuzzbench_dir()
            .unwrap_or_else(|_| PathBuf::from("./libafl_fuzzbench"));

        Self {
            harness_map,
            fuzzbench_dir,
            compiled_fuzzers: std::sync::Mutex::new(HashMap::new()),
            path_resolver,
        }
    }

    /// 选择合适的harness
    pub fn select_harness(&self, strategy: &DetailedStrategyConfig) -> Result<String> {
        // 基于策略配置选择harness
        let scheduler_type = &strategy.scheduler.name;

        // 检查变异器类型 - 现在需要检查嵌套的变异器配置
        let has_mopt = strategy
            .mutator
            .parameters
            .mutators
            .iter()
            .any(|m| m.name.contains("mopt"));

        // 优先级规则 - cmplog 优先级最高，因为需要特殊的harness支持
        if strategy.feedbacks.iter().any(|f| f.name.contains("cmp")) {
            return Ok("cmplog".to_string());
        }

        if has_mopt {
            return Ok("mopt".to_string());
        }

        if scheduler_type.contains("explore") {
            return Ok("explore".to_string());
        }

        if scheduler_type.contains("power") || scheduler_type.contains("weighted") {
            return Ok("generic".to_string());
        }

        // 默认使用fast
        Ok("fast".to_string())
    }

    /// 构建harness命令
    pub fn build_command(
        &self,
        harness_type: &str,
        target_path: &Path,
        work_dir: &Path,
        output_dir: &Path,
        strategy_file: &Path,
        instance_id: &str,
    ) -> Result<Command> {
        let harness_info = self
            .harness_map
            .get(harness_type)
            .ok_or_else(|| anyhow!("未知的harness类型: {}", harness_type))?;

        // 查找harness二进制文件
        let harness_binary = self.find_harness_binary(&harness_info.binary_name)?;

        info!(
            "使用harness: {} ({})",
            harness_type,
            harness_binary.display()
        );

        let mut cmd = Command::new(harness_binary);

        // 设置基本参数
        cmd.arg("--").arg(target_path);

        // 设置环境变量
        cmd.env("AFL_TARGET_PATH", target_path); // 添加目标路径环境变量
        cmd.env("AFL_BENCH_UNTIL_CRASH", "1");
        cmd.env("AFL_SKIP_BIN_CHECK", "1");
        cmd.env("AFL_NO_AFFINITY", "1");
        cmd.env("AFL_INSTANCE_ID", instance_id);

        // 设置工作目录相关环境变量
        cmd.env("AFL_TMPDIR", work_dir);
        cmd.env("AFL_OUT_DIR", output_dir);

        // 设置策略文件路径（自定义环境变量）
        cmd.env("LIBAFL_STRATEGY_FILE", strategy_file);

        // 设置输入/输出目录
        let input_dir = work_dir.join("corpus");
        let crash_dir = output_dir.join("crashes");

        // 创建必要的目录
        std::fs::create_dir_all(&input_dir)?;
        std::fs::create_dir_all(&crash_dir)?;

        // 创建初始种子文件（如果不存在）
        let seed_file = input_dir.join("seed");
        if !seed_file.exists() {
            std::fs::write(&seed_file, b"0")?;
        }

        cmd.env("AFL_IN_DIR", &input_dir);
        cmd.env("AFL_CRASH_DIR", &crash_dir);

        // 设置日志
        cmd.env("RUST_LOG", "info");
        cmd.env("RUST_BACKTRACE", "1");

        // 设置工作目录
        cmd.current_dir(work_dir);

        // 重定向输出
        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        Ok(cmd)
    }

    /// 查找harness二进制文件
    fn find_harness_binary(&self, binary_name: &str) -> Result<PathBuf> {
        // 首先检查我们自己的libafl-harness
        if binary_name == "generic_cc" || binary_name == "fast_cc" {
            // 使用 PathResolver 查找引擎路径
            if let Ok(engine_path) = self.path_resolver.get_engine_path() {
                if engine_path.exists() {
                    info!("使用PathResolver找到的引擎二进制: {engine_path:?}");
                    return Ok(engine_path);
                }
            }

            // 后备相对路径
            let relative_harness_paths = vec![
                PathBuf::from("./target/release/libafl-harness"),
                PathBuf::from("./target/debug/libafl-harness"),
                PathBuf::from("../target/release/libafl-harness"),
                PathBuf::from("../target/debug/libafl-harness"),
            ];

            for path in relative_harness_paths {
                if path.exists() {
                    info!("使用相对路径找到的libafl-harness: {path:?}");
                    return Ok(path);
                }
            }
        }

        // 可能的路径模式
        let possible_paths = vec![
            // Release版本
            self.fuzzbench_dir
                .join(format!("target/release/{binary_name}")),
            // Debug版本
            self.fuzzbench_dir
                .join(format!("target/debug/{binary_name}")),
            // 特定harness目录的release版本
            self.fuzzbench_dir.join(format!(
                "{}/target/release/{}",
                binary_name.trim_end_matches("_cc").trim_end_matches("_cxx"),
                binary_name
            )),
        ];

        for path in possible_paths {
            if path.exists() && path.is_file() {
                return Ok(path);
            }
        }

        // 尝试构建harness
        warn!("未找到预编译的harness {binary_name}，尝试构建...");

        let build_result = self.build_harness(binary_name)?;
        if build_result {
            // 重新查找
            let release_path = self
                .fuzzbench_dir
                .join(format!("target/release/{binary_name}"));
            if release_path.exists() {
                return Ok(release_path);
            }
        }

        Err(anyhow!("无法找到或构建harness: {}", binary_name))
    }

    /// 构建harness
    fn build_harness(&self, binary_name: &str) -> Result<bool> {
        let harness_name = binary_name.trim_end_matches("_cc").trim_end_matches("_cxx");

        info!("构建harness: {harness_name}");

        let output = Command::new("cargo")
            .arg("build")
            .arg("--release")
            .arg("--bin")
            .arg(binary_name)
            .current_dir(&self.fuzzbench_dir)
            .output()?;

        if output.status.success() {
            info!("✓ Harness {harness_name} 构建成功");
            Ok(true)
        } else {
            error!(
                "Harness {} 构建失败:\n{}",
                harness_name,
                String::from_utf8_lossy(&output.stderr)
            );
            Ok(false)
        }
    }

    /// 获取所有可用的harness信息
    pub fn list_harnesses(&self) -> Vec<(String, String)> {
        self.harness_map
            .iter()
            .map(|(name, info)| (name.clone(), info.description.clone()))
            .collect()
    }

    /// 验证策略是否与harness兼容
    pub fn validate_strategy(
        &self,
        harness_type: &str,
        strategy: &DetailedStrategyConfig,
    ) -> Result<()> {
        let harness_info = self
            .harness_map
            .get(harness_type)
            .ok_or_else(|| anyhow!("未知的harness类型: {}", harness_type))?;

        // 验证mutator
        if !harness_info
            .supported_mutators
            .iter()
            .any(|m| strategy.mutator.name.contains(m))
        {
            warn!(
                "Harness {} 可能不完全支持mutator: {}",
                harness_type, strategy.mutator.name
            );
        }

        // 验证scheduler
        if !harness_info
            .supported_schedulers
            .iter()
            .any(|s| strategy.scheduler.name.to_lowercase().contains(s))
        {
            warn!(
                "Harness {} 可能不完全支持scheduler: {}",
                harness_type, strategy.scheduler.name
            );
        }

        Ok(())
    }

    /// 构建tokio::process::Command
    pub fn build_tokio_command(
        &self,
        harness_type: &str,
        target_path: &Path,
        work_dir: &Path,
        output_dir: &Path,
        strategy_file: &Path,
        instance_id: &str,
    ) -> Result<tokio::process::Command> {
        let harness_info = self
            .harness_map
            .get(harness_type)
            .ok_or_else(|| anyhow!("未知的harness类型: {}", harness_type))?;

        // 查找harness二进制文件
        let harness_binary = self.find_harness_binary(&harness_info.binary_name)?;

        info!(
            "使用harness: {} ({})",
            harness_type,
            harness_binary.display()
        );

        let mut cmd = tokio::process::Command::new(harness_binary);

        // 设置基本参数
        cmd.arg("--").arg(target_path);

        // 设置环境变量
        cmd.env("AFL_TARGET_PATH", target_path); // 添加目标路径环境变量
        cmd.env("AFL_BENCH_UNTIL_CRASH", "1");
        cmd.env("AFL_SKIP_BIN_CHECK", "1");
        cmd.env("AFL_NO_AFFINITY", "1");
        cmd.env("AFL_INSTANCE_ID", instance_id);

        // 设置工作目录相关环境变量
        cmd.env("AFL_TMPDIR", work_dir);
        cmd.env("AFL_OUT_DIR", output_dir);

        // 设置策略文件路径（自定义环境变量）
        cmd.env("LIBAFL_STRATEGY_FILE", strategy_file);

        // 设置输入/输出目录
        let input_dir = work_dir.join("corpus");
        let crash_dir = output_dir.join("crashes");

        // 创建必要的目录
        std::fs::create_dir_all(&input_dir)?;
        std::fs::create_dir_all(&crash_dir)?;

        // 创建初始种子文件（如果不存在）
        let seed_file = input_dir.join("seed");
        if !seed_file.exists() {
            std::fs::write(&seed_file, b"0")?;
        }

        cmd.env("AFL_IN_DIR", &input_dir);
        cmd.env("AFL_CRASH_DIR", &crash_dir);

        // 设置日志
        cmd.env("RUST_LOG", "info");
        cmd.env("RUST_BACKTRACE", "1");

        // 设置工作目录
        cmd.current_dir(work_dir);

        // 重定向输出
        cmd.stdout(std::process::Stdio::piped());
        cmd.stderr(std::process::Stdio::piped());

        Ok(cmd)
    }

    /// 编译自定义fuzzer
    pub async fn compile_custom_fuzzer(
        &self,
        config: &CustomFuzzerConfig,
        work_dir: &Path,
    ) -> Result<PathBuf> {
        info!("编译自定义fuzzer: {}", config.name);

        // 检查缓存
        {
            let cache = self.compiled_fuzzers.lock().unwrap();
            if let Some(cached_path) = cache.get(&config.name) {
                if cached_path.exists() {
                    info!("使用缓存的fuzzer: {cached_path:?}");
                    return Ok(cached_path.clone());
                }
            }
        }

        // 如果没有源代码但有二进制路径，直接使用
        if config.source_path.is_none() {
            let binary_path = PathBuf::from(&config.binary_path);
            if binary_path.exists() {
                info!("使用预编译的fuzzer: {binary_path:?}");
                let mut cache = self.compiled_fuzzers.lock().unwrap();
                cache.insert(config.name.clone(), binary_path.clone());
                return Ok(binary_path);
            } else {
                return Err(anyhow!(
                    "未找到二进制文件，也没有源代码: {}",
                    config.binary_path
                ));
            }
        }

        // 创建编译目录
        let compile_dir = work_dir.join("custom_fuzzers").join(&config.name);
        tokio::fs::create_dir_all(&compile_dir).await?;

        // 复制源代码
        let source_path = PathBuf::from(config.source_path.as_ref().unwrap());
        if !source_path.exists() {
            return Err(anyhow!("源代码文件不存在: {:?}", source_path));
        }

        let target_source = compile_dir.join("main.rs");
        tokio::fs::copy(&source_path, &target_source).await?;

        // 获取 LibAFL 组件路径
        let libafl_components = self
            .path_resolver
            .get_libafl_components()
            .map_err(|e| anyhow!("无法获取LibAFL组件路径: {}", e))?;

        let (libafl_path, libafl_bolts_path, libafl_targets_path) = libafl_components
            .to_cargo_paths()
            .map_err(|e| anyhow!("无法生成Cargo路径: {}", e))?;

        // 创建最小化的Cargo.toml
        let cargo_toml = format!(
            r#"[package]
name = "{}"
version = "0.1.0"
edition = "2021"

[dependencies]
libafl = {{ path = "{}" }}
libafl_bolts = {{ path = "{}" }}
libafl_targets = {{ path = "{}" }}

[[bin]]
name = "{}"
path = "main.rs"
"#,
            config.name, libafl_path, libafl_bolts_path, libafl_targets_path, config.name
        );

        let cargo_toml_path = compile_dir.join("Cargo.toml");
        let mut file = tokio::fs::File::create(&cargo_toml_path).await?;
        file.write_all(cargo_toml.as_bytes()).await?;
        file.flush().await?;

        // 执行编译命令
        let default_command = "cargo build --release".to_string();
        let build_command = config.build_command.as_ref().unwrap_or(&default_command);

        info!("执行编译命令: {build_command}");

        let output = tokio::process::Command::new("sh")
            .arg("-c")
            .arg(build_command)
            .current_dir(&compile_dir)
            .output()
            .await?;

        if !output.status.success() {
            error!("编译失败:\n{}", String::from_utf8_lossy(&output.stderr));
            return Err(anyhow!("编译自定义fuzzer失败"));
        }

        // 查找编译后的二进制文件
        let binary_path = compile_dir.join("target/release").join(&config.name);
        if !binary_path.exists() {
            // 尝试debug版本
            let debug_path = compile_dir.join("target/debug").join(&config.name);
            if debug_path.exists() {
                info!("编译成功（debug版本）: {debug_path:?}");
                let mut cache = self.compiled_fuzzers.lock().unwrap();
                cache.insert(config.name.clone(), debug_path.clone());
                return Ok(debug_path);
            }
            return Err(anyhow!("编译后未找到二进制文件"));
        }

        info!("编译成功: {binary_path:?}");

        // 缓存结果
        {
            let mut cache = self.compiled_fuzzers.lock().unwrap();
            cache.insert(config.name.clone(), binary_path.clone());
        }

        Ok(binary_path)
    }

    /// 注册自定义fuzzer到harness映射
    pub fn register_custom_fuzzer(
        &mut self,
        name: String,
        binary_path: PathBuf,
        config: &DetailedStrategyConfig,
    ) {
        let harness_info = HarnessInfo {
            binary_name: name.clone(),
            description: format!("Custom fuzzer: {name}"),
            supported_mutators: vec![config.mutator.name.clone()],
            supported_schedulers: vec![config.scheduler.name.clone()],
        };

        self.harness_map.insert(name.clone(), harness_info);

        // 同时更新缓存
        let mut cache = self.compiled_fuzzers.lock().unwrap();
        cache.insert(name, binary_path);
    }
}

