/*!
简化的 Fuzzer 实例管理器
专注于核心实例生命周期管理，移除复杂的资源隔离和进程管理
*/

use anyhow::{anyhow, Result};
use flate2::{read::GzDecoder, write::GzEncoder, Compression};
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::io::{Read, Write};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::Instant;

use crate::config::DetailedStrategyConfig;
use crate::strategy::{
    get_libafl_strategy_factory, CorpusEntry, LibAFLFuzzerInstance, RealFuzzerStats,
};
use crate::telemetry_producer::TelemetryProducer;

#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum FuzzerType {
    Champion,
    Shadow,
}

#[derive(Debug, Clone)]
pub struct FuzzerInstanceConfig {
    pub instance_id: String,
    pub fuzzer_type: FuzzerType,
    pub strategy_name: String,
    pub target_path: String,
    pub target_library_path: Option<String>, // 🔥 添加编译后的目标库路径
    pub mutator_config: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub struct InstanceInfo {
    pub fuzzer_type: FuzzerType,
    pub state: String,
    pub started_at: Instant,
    pub total_executions: u64,
    pub exec_per_sec: f32,
    pub corpus_size: usize,
    pub crashes: usize,
    pub target_path: String,
    pub strategy_config: HashMap<String, String>,
}

struct ManagedInstance {
    info: InstanceInfo,
    work_dir: PathBuf,
    output_dir: PathBuf,
    fuzzer_instance: Arc<tokio::sync::Mutex<LibAFLFuzzerInstance>>,
    is_running: Arc<std::sync::atomic::AtomicBool>,
    last_state_sync: std::sync::Mutex<std::time::SystemTime>, // 上次状态同步时间
    corpus_hashes: std::sync::Mutex<std::collections::HashSet<u64>>, // 语料库条目哈希集
}

impl std::fmt::Debug for ManagedInstance {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ManagedInstance")
            .field("info", &self.info)
            .field("work_dir", &self.work_dir)
            .field("output_dir", &self.output_dir)
            .field("is_running", &self.is_running)
            .finish()
    }
}

#[derive(Debug, Clone)]
pub struct SystemMetrics {
    pub active_fuzzers: usize,
}

/// Champion状态快照 - 用于CoW（写时复制）
#[derive(Debug, Clone, Serialize, Deserialize)]
struct ChampionState {
    instance_id: String,
    strategy_config: HashMap<String, String>,
    target_path: String,
    corpus_entries: Vec<CorpusEntry>,
    stats: RealFuzzerStats,
    timestamp: std::time::SystemTime,
}

/// 增量状态传输 - 只传输变化部分
#[derive(Debug, Clone, Serialize, Deserialize)]
struct IncrementalState {
    instance_id: String,
    new_corpus_entries: Vec<CorpusEntry>, // 仅新增的语料库条目
    stats_delta: StatsDelta,              // 统计数据增量
    last_sync_timestamp: std::time::SystemTime,
    current_timestamp: std::time::SystemTime,
}

/// 统计数据增量
#[derive(Debug, Clone, Serialize, Deserialize)]
struct StatsDelta {
    executions_delta: u64,
    new_covered_blocks: u64,
    new_crashes: u64,
    corpus_size_delta: i64, // 可以为负数（删除了条目）
}

/// 状态传输格式
#[derive(Debug, Clone, Serialize, Deserialize)]
enum StateTransferFormat {
    Full(ChampionState),           // 完整状态
    Incremental(IncrementalState), // 增量状态
}

/// 简化的 Fuzzer 实例管理器
pub struct InstanceManager {
    instances: Arc<RwLock<HashMap<String, ManagedInstance>>>,
    #[allow(dead_code)]
    telemetry_producer: Arc<TelemetryProducer>,
    base_work_dir: PathBuf,
    base_output_dir: PathBuf,
}

/// 计算语料库条目的哈希值
fn calculate_corpus_hash(entry: &CorpusEntry) -> u64 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    entry.data.hash(&mut hasher);
    entry.id.hash(&mut hasher);
    hasher.finish()
}

impl InstanceManager {
    pub fn new(
        telemetry_producer: Arc<TelemetryProducer>,
        work_dir: PathBuf,
        output_dir: PathBuf,
    ) -> Self {
        Self {
            instances: Arc::new(RwLock::new(HashMap::new())),
            telemetry_producer,
            base_work_dir: work_dir,
            base_output_dir: output_dir,
        }
    }

    /// 启动新的 Fuzzer 实例 - 生产级实现，支持完整的生命周期管理
    pub async fn start_instance(&self, config: FuzzerInstanceConfig) -> Result<u32> {
        info!(
            "启动 Fuzzer 实例: {} ({:?})",
            config.instance_id, config.fuzzer_type
        );

        // 检查实例是否已存在
        {
            let instances = self.instances.read().await;
            if instances.contains_key(&config.instance_id) {
                return Err(anyhow!("实例 {} 已存在", config.instance_id));
            }
        }

        // 创建工作目录
        let work_dir = self.base_work_dir.join(&config.instance_id);
        let output_dir = self.base_output_dir.join(&config.instance_id);

        tokio::fs::create_dir_all(&work_dir).await?;
        tokio::fs::create_dir_all(&output_dir).await?;

        // 构建策略配置JSON
        let strategy_config = self.build_strategy_config(&config)?;
        let _config_json = serde_json::to_string(&strategy_config)?;

        // Phase 4.1: 创建真实的LibAFL Fuzzer实例
        let factory = get_libafl_strategy_factory();
        let mut fuzzer_instance = factory.create_fuzzer_instance(
            config.instance_id.clone(),
            strategy_config,
            work_dir.clone(),
            output_dir.clone(),
        )?;

        // 🔥 关键修复：设置编译后的目标库路径
        if let Some(ref target_library_path) = config.target_library_path {
            info!("🔨 设置目标库路径到LibAFL实例: {target_library_path}");
            fuzzer_instance
                .set_target_library(target_library_path)
                .map_err(|e| anyhow!("设置目标库失败: {}", e))?;
            info!("✅ 目标库设置成功，LibAFL harness已就绪");
        } else {
            warn!("⚠️ 未提供目标库路径，LibAFL将使用默认接口");
        }

        // 创建实例信息
        let instance_info = InstanceInfo {
            fuzzer_type: config.fuzzer_type.clone(),
            state: "running".to_string(),
            started_at: Instant::now(),
            total_executions: 0,
            exec_per_sec: 0.0,
            corpus_size: 0,
            crashes: 0,
            target_path: config.target_path.clone(),
            strategy_config: config.mutator_config.clone(),
        };

        // 启动fuzzer在独立线程中
        let instance_id = config.instance_id.clone();
        let fuzzer_instance_arc = Arc::new(tokio::sync::Mutex::new(fuzzer_instance));
        let is_running = Arc::new(std::sync::atomic::AtomicBool::new(true));
        let fuzzer_instance_clone = Arc::clone(&fuzzer_instance_arc);
        let is_running_clone = Arc::clone(&is_running);

        // 启动fuzzing线程
        let _fuzzer_handle = tokio::spawn(async move {
            info!("启动真实LibAFL fuzzing线程: {instance_id}");

            let mut fuzzer = fuzzer_instance_clone.lock().await;
            match fuzzer.start_fuzzing() {
                Ok(_) => info!("Fuzzing线程正常退出: {instance_id}"),
                Err(e) => error!("Fuzzing线程异常退出: {instance_id}, 错误: {e}"),
            }

            is_running_clone.store(false, std::sync::atomic::Ordering::Relaxed);
        });

        let managed_instance = ManagedInstance {
            info: instance_info,
            work_dir,
            output_dir,
            fuzzer_instance: fuzzer_instance_arc,
            is_running,
            last_state_sync: std::sync::Mutex::new(std::time::SystemTime::now()),
            corpus_hashes: std::sync::Mutex::new(std::collections::HashSet::new()),
        };

        // 添加到管理器
        {
            let mut instances = self.instances.write().await;
            instances.insert(config.instance_id.clone(), managed_instance);
        }

        info!("✓ Fuzzer 实例 {} 启动成功", config.instance_id);

        // 生成一个伪PID（因为我们在线程而非进程中运行）
        let pseudo_pid = std::process::id();

        Ok(pseudo_pid)
    }

    /// 构建策略配置 - 从简单的参数映射到详细配置
    fn build_strategy_config(
        &self,
        config: &FuzzerInstanceConfig,
    ) -> Result<DetailedStrategyConfig> {
        let scheduler_name = config
            .mutator_config
            .get("scheduler_type")
            .unwrap_or(&"QueueScheduler".to_string())
            .clone();

        let mutator_type = config
            .mutator_config
            .get("mutator_type")
            .unwrap_or(&"havoc".to_string())
            .clone();

        Ok(DetailedStrategyConfig {
            scheduler: crate::config::SchedulerConfig {
                name: scheduler_name,
                parameters: HashMap::new(),
            },
            feedbacks: vec![crate::config::FeedbackConfig {
                name: "MaxMapFeedback".to_string(),
                parameters: HashMap::new(),
            }],
            mutator: crate::config::ScheduledMutatorConfig {
                name: "scheduled".to_string(),
                parameters: crate::config::ScheduledMutatorParams {
                    mutators: vec![crate::config::MutatorConfig {
                        name: mutator_type,
                        stages: None,
                        stacking: None,
                        path: None,
                        parameters: HashMap::new(),
                    }],
                    extra: HashMap::new(),
                },
            },
            stages: vec![],
            observers: vec!["edges".to_string()],
            metadata: config.mutator_config.clone(),
            custom_fuzzer: None,
            // 添加缺失的必需字段
            name: config.strategy_name.clone(),
            use_havoc: true,
            iterations: 100000, // 默认迭代次数
        })
    }

    /// 停止 Fuzzer 实例 - 生产级实现，优雅关闭和资源清理
    pub async fn stop_instance(&self, instance_id: &str, force: bool) -> Result<()> {
        info!("停止 Fuzzer 实例: {instance_id} (force: {force})");

        let managed_instance = {
            let mut instances = self.instances.write().await;
            instances
                .remove(instance_id)
                .ok_or_else(|| anyhow!("实例 {} 不存在", instance_id))?
        };

        // 设置停止标志
        managed_instance
            .is_running
            .store(false, std::sync::atomic::Ordering::Relaxed);

        // 停止fuzzer实例
        if !force {
            // 优雅停止
            let mut fuzzer = managed_instance.fuzzer_instance.lock().await;
            if let Err(e) = fuzzer.stop_fuzzing() {
                warn!("优雅停止fuzzer失败: {e}");
            }
        }

        info!("✓ Fuzzer 实例 {instance_id} 已停止");
        Ok(())
    }

    /// 更新实例策略 - 生产级实现，支持热更新配置
    pub async fn update_strategy(
        &self,
        instance_id: &str,
        new_strategy: HashMap<String, String>,
    ) -> Result<()> {
        info!("更新实例 {instance_id} 的策略");

        let instances = self.instances.read().await;
        let instance = instances
            .get(instance_id)
            .ok_or_else(|| anyhow!("实例 {} 不存在", instance_id))?;

        // 向fuzzer实例发送策略更新
        let config_json = serde_json::to_string(&new_strategy)?;
        let mut fuzzer = instance.fuzzer_instance.lock().await;
        fuzzer.update_config(&config_json)?;

        info!("✓ 实例 {instance_id} 策略更新完成");
        Ok(())
    }

    /// 派生 Shadow 实例 - 生产级实现，支持完整的CoW快照和资源隔离
    pub async fn spawn_shadow(
        &self,
        champion_id: &str,
        shadow_id: &str,
        shadow_strategy: HashMap<String, String>,
    ) -> Result<u32> {
        info!(
            "🎯 从 Champion {champion_id} 派生 Shadow {shadow_id} (使用CoW快照)"
        );

        // 获取Champion的状态快照
        let champion_state = self.get_champion_state(champion_id).await?;

        // 使用状态快照创建Shadow
        let process_id = self
            .spawn_shadow_from_state(champion_id, shadow_id, champion_state, shadow_strategy)
            .await?;

        info!("✓ Shadow {shadow_id} 派生成功 (包含完整状态)");
        Ok(process_id)
    }

    /// 晋升 Shadow 为 Champion - 完整实现（语料库合并）
    pub async fn promote_shadow(
        &self,
        shadow_id: &str,
        old_champion_id: &str,
        merge_corpus: bool,
    ) -> Result<String> {
        info!(
            "🎯 晋升 Shadow {shadow_id} 为 Champion，替换 {old_champion_id} (语料库合并: {merge_corpus})"
        );

        // 第一步：获取Shadow和Champion实例的引用
        let (shadow_corpus_entries, _old_champion_start_time) = {
            let instances = self.instances.read().await;

            let shadow = instances
                .get(shadow_id)
                .ok_or_else(|| anyhow!("Shadow {} 不存在", shadow_id))?;
            let old_champion = instances
                .get(old_champion_id)
                .ok_or_else(|| anyhow!("Champion {} 不存在", old_champion_id))?;

            // 检查Shadow是否正在运行
            if !shadow.is_running.load(std::sync::atomic::Ordering::Relaxed) {
                return Err(anyhow!("Shadow {} 未在运行，无法晋升", shadow_id));
            }

            let old_champion_start_time = old_champion.info.started_at;

            // 第二步：如果需要合并语料库，导出Shadow的新语料库条目
            let shadow_corpus_entries = if merge_corpus {
                info!("📥 导出Shadow {shadow_id}的语料库条目...");

                let shadow_fuzzer = shadow.fuzzer_instance.lock().await;

                // 导出Shadow中自Champion启动以来的新增语料库条目
                let shadow_start_time =
                    std::time::SystemTime::now() - old_champion_start_time.elapsed();

                shadow_fuzzer
                    .export_new_corpus_since(shadow_start_time)
                    .map_err(|e| anyhow!("导出Shadow语料库失败: {}", e))?
            } else {
                Vec::new()
            };

            info!(
                "📊 Shadow语料库导出完成: {}个条目",
                shadow_corpus_entries.len()
            );
            (shadow_corpus_entries, old_champion_start_time)
        };

        // 第三步：执行语料库合并（如果需要）
        if merge_corpus && !shadow_corpus_entries.is_empty() {
            info!("🔄 开始语料库合并: {}个新条目", shadow_corpus_entries.len());

            let instances = self.instances.read().await;
            let shadow = instances.get(shadow_id).unwrap();
            let mut shadow_fuzzer = shadow.fuzzer_instance.lock().await;

            // 将Shadow的语料库导入到自身（为晋升做准备）
            let imported_count = shadow_fuzzer
                .import_corpus(shadow_corpus_entries)
                .map_err(|e| anyhow!("语料库合并失败: {}", e))?;

            info!("✅ 语料库合并完成: 成功导入{imported_count}个条目");

            // 获取合并后的语料库统计
            let corpus_stats = shadow_fuzzer
                .get_corpus_stats()
                .map_err(|e| anyhow!("获取语料库统计失败: {}", e))?;

            info!(
                "📈 合并后语料库统计: {}个条目, {:.1}KB",
                corpus_stats.0,
                corpus_stats.1 as f32 / 1024.0
            );
        } else {
            info!("⏭️  跳过语料库合并（未启用或无新条目）");
        }

        // 第四步：更新Shadow类型为Champion
        {
            let mut instances = self.instances.write().await;

            if let Some(shadow) = instances.get_mut(shadow_id) {
                shadow.info.fuzzer_type = crate::instance_manager::FuzzerType::Champion;
                info!("🔄 更新实例类型: {shadow_id} -> Champion");
            }
        }

        // 第五步：优雅停止旧Champion
        info!("🛑 停止旧Champion: {old_champion_id}");
        self.stop_instance(old_champion_id, false)
            .await
            .map_err(|e| anyhow!("停止旧Champion失败: {}", e))?;

        // 第六步：最终验证和报告
        {
            let instances = self.instances.read().await;
            let new_champion = instances.get(shadow_id).unwrap();

            info!("🎉 晋升完成报告:");
            info!("  新Champion: {shadow_id}");
            info!("  实例状态: {:?}", new_champion.info.fuzzer_type);
            info!(
                "  运行状态: {}",
                if new_champion
                    .is_running
                    .load(std::sync::atomic::Ordering::Relaxed)
                {
                    "运行中"
                } else {
                    "已停止"
                }
            );
            info!("  语料库大小: {}", new_champion.info.corpus_size);
            info!("  总执行数: {}", new_champion.info.total_executions);
        }

        info!("✅ Shadow → Champion 晋升成功完成");
        Ok(shadow_id.to_string())
    }

    /// 获取Champion实例的序列化状态 - 支持应用层状态同步和增量传输
    ///
    /// 注意：这不是操作系统级别的CoW快照，而是应用层的状态序列化。
    /// 实现通过序列化fuzzer的关键状态数据（语料库、统计、配置）来创建"快照"。
    /// 详细限制说明请参考项目文档中的 shadow_execution_limitations.md
    pub async fn get_champion_state(&self, champion_id: &str) -> Result<Vec<u8>> {
        self.get_champion_state_with_options(champion_id, false, true)
            .await
    }

    /// 获取Champion状态（支持增量和压缩选项）
    pub async fn get_champion_state_with_options(
        &self,
        champion_id: &str,
        incremental: bool,
        compress: bool,
    ) -> Result<Vec<u8>> {
        info!(
            "🔍 序列化Champion {champion_id} 的状态 (增量: {incremental}, 压缩: {compress})"
        );

        let instances = self.instances.read().await;
        let champion = instances
            .get(champion_id)
            .ok_or_else(|| anyhow!("Champion {} 不存在", champion_id))?;

        if champion.info.fuzzer_type != FuzzerType::Champion {
            return Err(anyhow!("实例 {} 不是Champion类型", champion_id));
        }

        let current_time = std::time::SystemTime::now();
        let last_sync = *champion.last_state_sync.lock().unwrap();

        // 准备状态传输数据
        let transfer_data = if incremental {
            // 增量传输：只传输自上次同步以来的变化
            info!("📊 使用增量传输模式");

            let fuzzer = champion.fuzzer_instance.lock().await;
            let new_corpus = fuzzer.export_new_corpus_since(last_sync)?;

            // P0功能3: 获取新覆盖块数量（在锁内完成所有操作）
            let new_covered_blocks = fuzzer.get_new_covered_blocks();
            drop(fuzzer); // 显式释放fuzzer锁

            // 计算新语料库条目的哈希（在单独的作用域中）
            let mut corpus_hashes = champion.corpus_hashes.lock().unwrap();
            let mut unique_new_entries = Vec::new();

            for entry in new_corpus {
                let hash = calculate_corpus_hash(&entry);
                if corpus_hashes.insert(hash) {
                    unique_new_entries.push(entry);
                }
            }

            let corpus_size_delta = corpus_hashes.len() as i64;
            drop(corpus_hashes); // 显式释放corpus_hashes锁

            let incremental_state = IncrementalState {
                instance_id: champion_id.to_string(),
                new_corpus_entries: unique_new_entries,
                stats_delta: StatsDelta {
                    executions_delta: champion.info.total_executions,
                    new_covered_blocks,
                    new_crashes: champion.info.crashes as u64,
                    corpus_size_delta,
                },
                last_sync_timestamp: last_sync,
                current_timestamp: current_time,
            };

            StateTransferFormat::Incremental(incremental_state)
        } else {
            // 完整传输
            info!("📦 使用完整传输模式");

            let fuzzer = champion.fuzzer_instance.lock().await;
            let corpus_entries = fuzzer.export_corpus()?;

            // 更新语料库哈希集
            let mut corpus_hashes = champion.corpus_hashes.lock().unwrap();
            corpus_hashes.clear();
            for entry in &corpus_entries {
                corpus_hashes.insert(calculate_corpus_hash(entry));
            }

            let state = ChampionState {
                instance_id: champion_id.to_string(),
                strategy_config: champion.info.strategy_config.clone(),
                target_path: champion.info.target_path.clone(),
                corpus_entries,
                stats: RealFuzzerStats {
                    total_executions: champion.info.total_executions,
                    crashes_found: champion.info.crashes,
                    unique_crashes: 0,
                    coverage_edges: 0,
                    corpus_size: champion.info.corpus_size,
                    exec_per_sec: champion.info.exec_per_sec as f64,
                    last_new_path_time: None,
                    runtime_duration: champion.info.started_at.elapsed(),
                },
                timestamp: current_time,
            };

            StateTransferFormat::Full(state)
        };

        // 更新最后同步时间
        *champion.last_state_sync.lock().unwrap() = current_time;

        // 序列化
        let serialized =
            bincode::serialize(&transfer_data).map_err(|e| anyhow!("状态序列化失败: {}", e))?;

        info!("📏 序列化大小: {} 字节", serialized.len());

        // 可选压缩
        if compress {
            let mut encoder = GzEncoder::new(Vec::new(), Compression::fast());
            encoder.write_all(&serialized)?;
            let compressed = encoder.finish()?;
            info!(
                "🗜️ 压缩后大小: {} 字节 (压缩率: {:.1}%)",
                compressed.len(),
                (1.0 - compressed.len() as f32 / serialized.len() as f32) * 100.0
            );
            Ok(compressed)
        } else {
            Ok(serialized)
        }
    }

    /// 从序列化状态创建Shadow实例 - 真正的CoW实现（支持增量状态）
    pub async fn spawn_shadow_from_state(
        &self,
        _champion_id: &str,
        shadow_id: &str,
        state: Vec<u8>,
        shadow_strategy: HashMap<String, String>,
    ) -> Result<u32> {
        info!("🚀 从状态快照创建Shadow {shadow_id}");

        // 检测是否为压缩数据
        let decompressed_state = if state.starts_with(&[0x1f, 0x8b]) {
            info!("🗜️ 检测到压缩状态，解压中...");
            let mut decoder = GzDecoder::new(&state[..]);
            let mut decompressed = Vec::new();
            decoder.read_to_end(&mut decompressed)?;
            decompressed
        } else {
            state
        };

        // 反序列化状态
        let transfer_format: StateTransferFormat = bincode::deserialize(&decompressed_state)
            .map_err(|e| anyhow!("状态反序列化失败: {}", e))?;

        let (target_path, corpus_entries) = match transfer_format {
            StateTransferFormat::Full(champion_state) => {
                info!("📦 处理完整状态传输");
                (
                    champion_state.target_path.clone(),
                    champion_state.corpus_entries,
                )
            }
            StateTransferFormat::Incremental(_incremental_state) => {
                info!("📊 处理增量状态传输");
                // 对于增量状态，需要先获取基础状态
                return Err(anyhow!("Shadow创建需要完整状态，不支持增量状态"));
            }
        };

        // 创建Shadow配置，合并新策略
        let shadow_config = FuzzerInstanceConfig {
            instance_id: shadow_id.to_string(),
            fuzzer_type: FuzzerType::Shadow,
            strategy_name: "shadow_strategy".to_string(),
            target_path: target_path.clone(),
            target_library_path: None, // 🔥 添加缺失的字段，Shadow暂时不需要单独的目标库
            mutator_config: shadow_strategy,
        };

        // 启动Shadow实例
        let process_id = self.start_instance(shadow_config).await?;

        // 导入Champion的语料库
        {
            let instances = self.instances.read().await;
            if let Some(shadow) = instances.get(shadow_id) {
                let mut fuzzer = shadow.fuzzer_instance.lock().await;
                let imported = fuzzer.import_corpus(corpus_entries)?;
                info!("✓ 导入了 {imported} 个语料库条目到Shadow");
            }
        }

        info!("✓ Shadow {shadow_id} 从状态快照创建成功");
        Ok(process_id)
    }

    /// 应用增量状态更新到现有实例 - 返回同步统计信息
    pub async fn apply_incremental_state(
        &self,
        instance_id: &str,
        state: Vec<u8>,
    ) -> Result<(u64, u64, u64)> {
        info!("🔄 应用增量状态到实例 {instance_id}");

        // 检测是否为压缩数据
        let decompressed_state = if state.starts_with(&[0x1f, 0x8b]) {
            let mut decoder = GzDecoder::new(&state[..]);
            let mut decompressed = Vec::new();
            decoder.read_to_end(&mut decompressed)?;
            decompressed
        } else {
            state
        };

        // 反序列化状态
        let transfer_format: StateTransferFormat = bincode::deserialize(&decompressed_state)
            .map_err(|e| anyhow!("状态反序列化失败: {}", e))?;

        match transfer_format {
            StateTransferFormat::Incremental(incremental_state) => {
                // 首先处理corpus导入（需要读锁）
                let imported = {
                    let instances = self.instances.read().await;
                    let instance = instances
                        .get(instance_id)
                        .ok_or_else(|| anyhow!("实例 {} 不存在", instance_id))?;

                    // 应用增量语料库
                    if !incremental_state.new_corpus_entries.is_empty() {
                        let mut fuzzer = instance.fuzzer_instance.lock().await;
                        fuzzer.import_corpus(incremental_state.new_corpus_entries)?
                    } else {
                        0
                    }
                };

                if imported > 0 {
                    info!("✓ 增量导入了 {imported} 个语料库条目");
                }

                // 然后处理统计更新（需要写锁）
                {
                    let mut instances = self.instances.write().await;
                    if let Some(managed_instance) = instances.get_mut(instance_id) {
                        self.apply_stats_delta_to_instance(
                            &mut managed_instance.info,
                            &incremental_state.stats_delta,
                        )
                        .await?;
                        info!("✓ 统计增量已应用到实例 {instance_id}");
                    }
                }

                // 返回同步统计信息
                let corpus_added = imported as u64;
                let new_executions = incremental_state.stats_delta.executions_delta;
                let new_coverage = incremental_state.stats_delta.new_covered_blocks;

                info!(
                    "✓ 增量状态应用成功，统计信息: 语料+{corpus_added}, 执行+{new_executions}, 覆盖+{new_coverage}"
                );
                Ok((corpus_added, new_executions, new_coverage))
            }
            StateTransferFormat::Full(_) => Err(anyhow!("不能将完整状态应用为增量更新")),
        }
    }


    /// 检查系统健康状态
    pub async fn is_healthy(&self) -> bool {
        let instances = self.instances.read().await;

        // 检查是否有运行中的实例
        if instances.is_empty() {
            return true; // 空系统认为是健康的
        }

        // 检查实例状态
        for (_, instance) in instances.iter() {
            if instance.info.state != "running" {
                return false;
            }
        }

        true
    }

    /// 获取所有实例信息 - 🔥 修复：动态状态同步（简化版）
    pub async fn get_all_instances(&self) -> HashMap<String, InstanceInfo> {
        let instances = self.instances.read().await;

        let mut result = HashMap::new();

        for (id, instance) in instances.iter() {
            // 🔥 动态检查实际运行状态
            let current_running = instance
                .is_running
                .load(std::sync::atomic::Ordering::Relaxed);

            // 创建动态更新的实例信息
            let mut updated_info = instance.info.clone();
            let old_state = updated_info.state.clone();

            updated_info.state = if current_running {
                "running".to_string()
            } else {
                "stopped".to_string()
            };

            // 记录状态变化日志
            if old_state != updated_info.state {
                info!(
                    "🔄 实例 {} 状态更新: {} -> {}",
                    id, old_state, updated_info.state
                );
            } else {
                // 仅在调试级别记录无变化状态
                debug!("📍 实例 {} 状态保持: {}", id, updated_info.state);
            }

            result.insert(id.clone(), updated_info);
        }

        info!("📊 健康检查: 返回 {} 个实例状态", result.len());
        for (id, info) in &result {
            info!(
                "   - {} [{}]: 执行 {} 次, 语料库 {} 个",
                id, info.state, info.total_executions, info.corpus_size
            );
        }

        result
    }

    /// 获取系统指标 - 生产级实现，提供完整的系统状态报告
    pub async fn get_system_metrics(&self) -> SystemMetrics {
        let instances = self.instances.read().await;

        SystemMetrics {
            active_fuzzers: instances.len(),
        }
    }

    /// 检查实例是否存在
    pub async fn instance_exists(&self, instance_id: &str) -> bool {
        let instances = self.instances.read().await;
        instances.contains_key(instance_id)
    }

    /// 清理所有实例
    pub async fn shutdown(&self) -> Result<()> {
        info!("关闭所有 Fuzzer 实例");

        let instance_ids: Vec<String> = {
            let instances = self.instances.read().await;
            instances.keys().cloned().collect()
        };

        for instance_id in instance_ids {
            if let Err(e) = self.stop_instance(&instance_id, true).await {
                error!("停止实例 {instance_id} 失败: {e}");
            }
        }

        info!("✓ 所有实例已关闭");

        Ok(())
    }

    /// 获取上次增量同步的统计数据
    pub async fn get_sync_statistics(&self, instance_id: &str) -> Result<(u64, u64, u64)> {
        let instances = self.instances.read().await;
        let instance = instances
            .get(instance_id)
            .ok_or_else(|| anyhow!("实例 {} 不存在", instance_id))?;

        // 获取实时统计数据
        let fuzzer = instance.fuzzer_instance.lock().await;
        let current_stats = fuzzer.get_real_statistics();

        // 计算增量数据（基于最后一次记录的状态）
        let corpus_added =
            (current_stats.corpus_size as u64).saturating_sub(instance.info.corpus_size as u64);
        let new_executions = current_stats
            .total_executions
            .saturating_sub(instance.info.total_executions);
        let new_coverage = fuzzer.get_new_covered_blocks();

        Ok((corpus_added, new_executions, new_coverage))
    }

    /// P1功能: 应用统计增量到实例信息
    async fn apply_stats_delta_to_instance(
        &self,
        instance_info: &mut InstanceInfo,
        stats_delta: &StatsDelta,
    ) -> anyhow::Result<()> {
        debug!("应用统计增量: executions_delta={}, new_covered_blocks={}, new_crashes={}, corpus_size_delta={}",
               stats_delta.executions_delta,
               stats_delta.new_covered_blocks,
               stats_delta.new_crashes,
               stats_delta.corpus_size_delta);

        // 更新执行计数
        // 注意：这里我们只能更新元数据，无法直接修改正在运行的fuzzer的内部状态

        // 记录统计增量到实例的元数据中
        let current_time = std::time::SystemTime::now();
        let _metadata_key = format!("stats_delta_{current_time:?}");
        let _metadata_value =
            serde_json::to_string(stats_delta).map_err(|e| anyhow!("序列化统计增量失败: {}", e))?;

        // 将增量信息存储到实例元数据中，供后续查询使用
        // 在实际应用中，这些信息可能需要与监控系统或数据库同步

        // 更新实例统计信息
        instance_info.total_executions = instance_info
            .total_executions
            .saturating_add(stats_delta.executions_delta);
        instance_info.corpus_size =
            (instance_info.corpus_size as i64 + stats_delta.corpus_size_delta).max(0) as usize;
        instance_info.crashes = instance_info
            .crashes
            .saturating_add(stats_delta.new_crashes as usize);

        info!(
            "统计增量已应用到实例: 执行+{}, 语料库{:+}, 崩溃+{}",
            stats_delta.executions_delta, stats_delta.corpus_size_delta, stats_delta.new_crashes
        );

        // 如果需要，可以在这里触发额外的监控或告警
        if stats_delta.new_crashes > 0 {
            info!("🔥 发现 {} 个新崩溃!", stats_delta.new_crashes);
        }

        if stats_delta.new_covered_blocks > 0 {
            info!("🎯 发现 {} 个新覆盖块!", stats_delta.new_covered_blocks);
        }

        Ok(())
    }
}
