/*!
复合验证沙箱模块
实现workflow.md要求的代码验证流程：
- 静态安全分析（clippy, miri, cargo-audit）
- 编译验证
- 动态行为与效用探测
*/

use anyhow::{anyhow, Result};
use log::{info, warn};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::time::{Duration, Instant};
use tempfile::TempDir;
use tokio::fs;
use tokio::time::timeout;

/// 验证步骤枚举
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub enum ValidationStep {
    StaticAnalysis,
    Compilation,
    DynamicBehavior,
    UtilityProbe,
}

/// 验证结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ValidationResult {
    pub step: ValidationStep,
    pub passed: bool,
    pub message: String,
    pub metrics: HashMap<String, f32>,
    pub duration: Duration,
}

/// 验证配置
#[derive(Debug, <PERSON>lone)]
pub struct ValidationConfig {
    pub timeout_per_step: Duration,
    pub max_retries: usize,
    pub enable_miri: bool,
    pub enable_audit: bool,
    pub utility_thresholds: UtilityThresholds,
}

/// 效用探测阈值
#[derive(Debug, Clone)]
pub struct UtilityThresholds {
    pub min_mutation_rate: f32,   // 最小变异率
    pub min_output_entropy: f32,  // 最小输出熵
    pub min_mutator_usage: f32,   // 最小原子变异器利用率
    pub max_exec_latency_ms: f32, // 最大执行延迟
}

impl Default for ValidationConfig {
    fn default() -> Self {
        Self {
            timeout_per_step: Duration::from_secs(60),
            max_retries: 3,
            enable_miri: false, // Miri较慢，默认关闭
            enable_audit: true,
            utility_thresholds: UtilityThresholds {
                min_mutation_rate: 0.20,   // 20%最小变异率
                min_output_entropy: 2.0,   // 最小熵值
                min_mutator_usage: 0.30,   // 30%最小使用率
                max_exec_latency_ms: 10.0, // 10ms最大延迟
            },
        }
    }
}

/// 复合验证沙箱
pub struct CompoundValidationSandbox {
    config: ValidationConfig,
    work_dir: PathBuf,
}

impl CompoundValidationSandbox {
    /// 创建新的验证沙箱
    pub fn new(config: ValidationConfig) -> Result<Self> {
        // 使用项目根目录下的runtime/validation目录
        let work_dir = std::env::var("FUZZLM_VALIDATION_DIR")
            .map(PathBuf::from)
            .unwrap_or_else(|_| {
                // 默认使用runtime/validation目录
                let mut path = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
                // 如果在fuzzing-engine目录中，回到项目根目录
                if path.ends_with("fuzzing-engine") {
                    path.pop();
                    path.pop();
                }
                path.join("runtime").join("validation")
            });

        std::fs::create_dir_all(&work_dir)?;

        Ok(Self { config, work_dir })
    }

    /// 执行完整的复合验证流程
    pub async fn validate_code(
        &self,
        code: &str,
        language: &str,
        steps: Vec<ValidationStep>,
    ) -> Result<(bool, Vec<ValidationResult>, Option<PathBuf>)> {
        info!("开始复合验证，语言: {language}, 步骤: {steps:?}");

        // 创建临时工作目录
        let temp_dir = TempDir::new_in(&self.work_dir)?;
        let project_dir = temp_dir.path();

        // 根据语言初始化项目
        self.init_project(project_dir, language, code).await?;

        let mut results = Vec::new();
        let mut all_passed = true;

        // 执行各验证步骤
        for step in steps {
            let start = Instant::now();
            let result = match step {
                ValidationStep::StaticAnalysis => {
                    self.run_static_analysis(project_dir, language).await
                }
                ValidationStep::Compilation => self.run_compilation(project_dir, language).await,
                ValidationStep::DynamicBehavior => {
                    self.run_dynamic_behavior_test(project_dir, language).await
                }
                ValidationStep::UtilityProbe => self.run_utility_probe(project_dir, language).await,
            };

            let mut validation_result = match result {
                Ok(res) => res,
                Err(e) => ValidationResult {
                    step: step.clone(),
                    passed: false,
                    message: format!("验证失败: {e}"),
                    metrics: HashMap::new(),
                    duration: start.elapsed(),
                },
            };

            validation_result.duration = start.elapsed();

            if !validation_result.passed {
                all_passed = false;
                // 如果是关键步骤失败，提前终止
                if matches!(step, ValidationStep::Compilation) {
                    results.push(validation_result);
                    break;
                }
            }

            results.push(validation_result);
        }

        // 如果所有验证通过，返回编译产物路径
        let compiled_library = if all_passed {
            self.get_compiled_library_path(project_dir, language)
                .await
                .ok()
        } else {
            None
        };

        Ok((all_passed, results, compiled_library))
    }

    /// 初始化项目结构
    async fn init_project(&self, project_dir: &Path, language: &str, code: &str) -> Result<()> {
        match language {
            "rust" => {
                // 创建Cargo项目
                let cargo_toml = r#"[package]
name = "custom_mutator"
version = "0.1.0"
edition = "2021"

[dependencies]
libafl = { path = "../../../../../LibAFL/libafl" }
libafl_bolts = { path = "../../../../../LibAFL/libafl_bolts" }

[lib]
crate-type = ["cdylib"]
"#;

                fs::write(project_dir.join("Cargo.toml"), cargo_toml).await?;
                fs::create_dir_all(project_dir.join("src")).await?;
                fs::write(project_dir.join("src/lib.rs"), code).await?;
            }
            "c" | "cpp" => {
                // 创建C/C++项目
                let makefile = r#"
CC = clang
CXX = clang++
CFLAGS = -fPIC -O2 -Wall
LDFLAGS = -shared

all: mutator.so

mutator.so: mutator.o
	$(CC) $(LDFLAGS) -o $@ $^

mutator.o: mutator.c
	$(CC) $(CFLAGS) -c -o $@ $<

clean:
	rm -f *.o *.so
"#;

                fs::write(project_dir.join("Makefile"), makefile).await?;
                let filename = if language == "c" {
                    "mutator.c"
                } else {
                    "mutator.cpp"
                };
                fs::write(project_dir.join(filename), code).await?;
            }
            _ => return Err(anyhow!("不支持的语言: {}", language)),
        }

        Ok(())
    }

    /// 运行静态分析
    async fn run_static_analysis(
        &self,
        project_dir: &Path,
        language: &str,
    ) -> Result<ValidationResult> {
        info!("运行静态安全分析");

        let mut metrics = HashMap::new();
        let mut messages = Vec::new();
        let mut all_passed = true;

        match language {
            "rust" => {
                // 运行 clippy
                let clippy_result = self
                    .run_command("cargo", &["clippy", "--", "-D", "warnings"], project_dir)
                    .await;

                match clippy_result {
                    Ok(output) => {
                        if output.status.success() {
                            messages.push("✓ Clippy检查通过".to_string());
                            metrics.insert("clippy_warnings".to_string(), 0.0);
                        } else {
                            messages.push(format!(
                                "✗ Clippy发现警告:\n{}",
                                String::from_utf8_lossy(&output.stderr)
                            ));
                            all_passed = false;
                            metrics.insert("clippy_warnings".to_string(), 1.0);
                        }
                    }
                    Err(e) => {
                        messages.push(format!("✗ Clippy执行失败: {e}"));
                        all_passed = false;
                    }
                }

                // 运行 cargo-audit（如果启用）
                if self.config.enable_audit {
                    let audit_result = self.run_command("cargo", &["audit"], project_dir).await;

                    match audit_result {
                        Ok(output) => {
                            if output.status.success() {
                                messages.push("✓ 安全审计通过".to_string());
                                metrics.insert("security_vulnerabilities".to_string(), 0.0);
                            } else {
                                messages.push("⚠ 发现安全漏洞（非阻塞）".to_string());
                                // 安全审计失败不阻塞验证
                            }
                        }
                        Err(_) => {
                            messages.push("⚠ cargo-audit未安装，跳过安全审计".to_string());
                        }
                    }
                }

                // 运行 miri（如果启用）
                if self.config.enable_miri {
                    warn!("Miri检查已启用但未实现（需要特殊环境）");
                }
            }
            "c" | "cpp" => {
                // 运行 clang-tidy
                let tidy_result = self
                    .run_command(
                        "clang-tidy",
                        &[
                            "-checks=*",
                            "-warnings-as-errors=*",
                            if language == "c" {
                                "mutator.c"
                            } else {
                                "mutator.cpp"
                            },
                        ],
                        project_dir,
                    )
                    .await;

                match tidy_result {
                    Ok(output) => {
                        if output.status.success() {
                            messages.push("✓ Clang-tidy检查通过".to_string());
                        } else {
                            messages.push("⚠ Clang-tidy发现问题（非阻塞）".to_string());
                        }
                    }
                    Err(_) => {
                        messages.push("⚠ clang-tidy未安装，跳过静态分析".to_string());
                    }
                }
            }
            _ => {}
        }

        Ok(ValidationResult {
            step: ValidationStep::StaticAnalysis,
            passed: all_passed,
            message: messages.join("\n"),
            metrics,
            duration: Duration::default(),
        })
    }

    /// 运行编译
    async fn run_compilation(
        &self,
        project_dir: &Path,
        language: &str,
    ) -> Result<ValidationResult> {
        info!("运行编译验证");

        let start = Instant::now();
        let mut metrics = HashMap::new();

        let result = match language {
            "rust" => {
                self.run_command("cargo", &["build", "--release"], project_dir)
                    .await
            }
            "c" | "cpp" => self.run_command("make", &[], project_dir).await,
            _ => return Err(anyhow!("不支持的语言: {}", language)),
        };

        let compile_time = start.elapsed().as_secs_f32();
        metrics.insert("compile_time_seconds".to_string(), compile_time);

        match result {
            Ok(output) => {
                if output.status.success() {
                    Ok(ValidationResult {
                        step: ValidationStep::Compilation,
                        passed: true,
                        message: format!("✓ 编译成功，耗时: {compile_time:.2}秒"),
                        metrics,
                        duration: Duration::default(),
                    })
                } else {
                    Ok(ValidationResult {
                        step: ValidationStep::Compilation,
                        passed: false,
                        message: format!(
                            "✗ 编译失败:\n{}",
                            String::from_utf8_lossy(&output.stderr)
                        ),
                        metrics,
                        duration: Duration::default(),
                    })
                }
            }
            Err(e) => Ok(ValidationResult {
                step: ValidationStep::Compilation,
                passed: false,
                message: format!("✗ 编译命令执行失败: {e}"),
                metrics,
                duration: Duration::default(),
            }),
        }
    }

    /// 运行动态行为与效用探测 - 完整实现workflow.md要求
    async fn run_dynamic_behavior_test(
        &self,
        project_dir: &Path,
        language: &str,
    ) -> Result<ValidationResult> {
        info!("运行动态行为与效用探测 - 完整的workflow.md实现");

        let mut metrics = HashMap::new();
        let mut messages = Vec::new();

        // 获取编译后的库路径
        let library_path = self
            .get_compiled_library_path(project_dir, language)
            .await?;

        // 第一步：基本库加载验证
        let load_test_result = tokio::task::spawn_blocking({
            let library_path = library_path.clone();
            move || unsafe {
                let lib = libloading::Library::new(&library_path)?;
                let _: libloading::Symbol<unsafe extern "C" fn()> = lib
                    .get(b"custom_mutator_init")
                    .or_else(|_| lib.get(b"init"))?;
                Ok::<_, anyhow::Error>(())
            }
        })
        .await?;

        if let Err(e) = load_test_result {
            return Ok(ValidationResult {
                step: ValidationStep::DynamicBehavior,
                passed: false,
                message: format!("✗ 动态库加载失败: {e}"),
                metrics,
                duration: Duration::default(),
            });
        }

        messages.push("✓ 动态库加载成功".to_string());

        // 第二步：完整的动态行为与效用探测（workflow.md要求）
        // 创建测试harness
        let test_harness_code = self.create_test_harness(language);
        let test_dir = project_dir.join("dynamic_behavior_test");
        tokio::fs::create_dir_all(&test_dir).await?;

        // 写入测试harness
        let harness_file = if language == "rust" {
            test_dir.join("src/main.rs")
        } else {
            test_dir.join("test_harness.c")
        };

        if language == "rust" {
            tokio::fs::create_dir_all(test_dir.join("src")).await?;
            self.create_test_cargo_toml(&test_dir, &library_path)
                .await?;
        }

        tokio::fs::write(&harness_file, test_harness_code).await?;

        // 编译测试harness
        let compile_result = if language == "rust" {
            self.run_command("cargo", &["build", "--release"], &test_dir)
                .await
        } else {
            self.run_command(
                "clang",
                &[
                    "-o",
                    "test_harness",
                    "test_harness.c",
                    "-L.",
                    "-lmutator",
                    "-Wl,-rpath,.",
                ],
                &test_dir,
            )
            .await
        }?;

        if !compile_result.status.success() {
            return Ok(ValidationResult {
                step: ValidationStep::DynamicBehavior,
                passed: false,
                message: format!(
                    "✗ 测试harness编译失败: {}",
                    String::from_utf8_lossy(&compile_result.stderr)
                ),
                metrics,
                duration: Duration::default(),
            });
        }

        messages.push("✓ 测试harness编译成功".to_string());

        // 第三步：使用多样化种子执行100次测试（workflow.md核心要求）
        let test_binary = if language == "rust" {
            test_dir.join("target/release/dynamic_behavior_test")
        } else {
            test_dir.join("test_harness")
        };

        // 创建多样化种子集（workflow.md要求：短、长、结构化、二进制）
        let test_seeds = self.create_diverse_test_seeds();
        let mut total_mutations = 0;
        let mut successful_mutations = 0;
        let mut unique_outputs = std::collections::HashSet::new();
        let mut total_latency_ns = 0u128;
        let mut execution_count = 0;
        let mut component_invocations = 0;

        messages.push(format!("✓ 创建{}个多样化种子", test_seeds.len()));

        // 对每个种子运行100次变异测试（workflow.md要求）
        for (seed_idx, seed) in test_seeds.iter().enumerate() {
            let seed_file = test_dir.join(format!("seed_{seed_idx}"));
            tokio::fs::write(&seed_file, seed).await?;

            // 每个种子运行100次（workflow.md明确要求）
            for _ in 0..100 {
                let start = std::time::Instant::now();

                let output = timeout(
                    Duration::from_millis(100),
                    tokio::process::Command::new(&test_binary)
                        .arg(&seed_file)
                        .output(),
                )
                .await??;

                let latency = start.elapsed();
                total_latency_ns += latency.as_nanos();
                execution_count += 1;
                component_invocations += 1;

                if output.status.success() {
                    successful_mutations += 1;

                    // 收集输出用于熵计算
                    if !output.stdout.is_empty() {
                        unique_outputs.insert(output.stdout.clone());
                    }
                }

                total_mutations += 1;
            }
        }

        // 第四步：计算关键效用指标（workflow.md要求）
        let mutation_rate = successful_mutations as f32 / total_mutations as f32;
        let output_entropy = self.calculate_entropy(&unique_outputs);
        let mutator_usage = unique_outputs.len() as f32 / total_mutations as f32;
        let avg_latency_ms = (total_latency_ns / execution_count as u128) as f32 / 1_000_000.0;

        // 存储指标
        metrics.insert("mutation_rate".to_string(), mutation_rate);
        metrics.insert("output_entropy".to_string(), output_entropy);
        metrics.insert("mutator_usage".to_string(), mutator_usage);
        metrics.insert("avg_latency_ms".to_string(), avg_latency_ms);
        metrics.insert(
            "total_component_invocations".to_string(),
            component_invocations as f32,
        );
        metrics.insert("total_seeds_tested".to_string(), test_seeds.len() as f32);

        messages.push(format!("✓ 完成{component_invocations}次组件调用测试"));

        // 第五步：根据预设门槛评估效用指标（workflow.md要求）
        let thresholds = &self.config.utility_thresholds;
        let mut passed = true;

        if mutation_rate < thresholds.min_mutation_rate {
            messages.push(format!(
                "✗ 变异率过低: {:.1}% < {:.1}%",
                mutation_rate * 100.0,
                thresholds.min_mutation_rate * 100.0
            ));
            passed = false;
        } else {
            messages.push(format!("✓ 变异率达标: {:.1}%", mutation_rate * 100.0));
        }

        if output_entropy < thresholds.min_output_entropy {
            messages.push(format!(
                "✗ 输出熵过低: {:.2} < {:.2}",
                output_entropy, thresholds.min_output_entropy
            ));
            passed = false;
        } else {
            messages.push(format!("✓ 输出熵达标: {output_entropy:.2}"));
        }

        if mutator_usage < thresholds.min_mutator_usage {
            messages.push(format!(
                "✗ 原子变异器利用率过低: {:.1}% < {:.1}%",
                mutator_usage * 100.0,
                thresholds.min_mutator_usage * 100.0
            ));
            passed = false;
        } else {
            messages.push(format!(
                "✓ 原子变异器利用率达标: {:.1}%",
                mutator_usage * 100.0
            ));
        }

        if avg_latency_ms > thresholds.max_exec_latency_ms {
            messages.push(format!(
                "✗ 平均执行延迟过高: {:.1}ms > {:.1}ms",
                avg_latency_ms, thresholds.max_exec_latency_ms
            ));
            passed = false;
        } else {
            messages.push(format!("✓ 平均执行延迟合格: {avg_latency_ms:.1}ms"));
        }

        // 生成最终报告
        if passed {
            messages.push("✅ 动态行为与效用探测：全部指标达标".to_string());
        } else {
            messages.push("❌ 动态行为与效用探测：部分指标未达标".to_string());
        }

        Ok(ValidationResult {
            step: ValidationStep::DynamicBehavior,
            passed,
            message: messages.join("\n"),
            metrics,
            duration: Duration::default(),
        })
    }

    /// 运行效用探测 - 简化为辅助验证功能（已整合到dynamic_behavior_test）
    async fn run_utility_probe(
        &self,
        project_dir: &Path,
        language: &str,
    ) -> Result<ValidationResult> {
        info!("运行效用探测 - 轻量级辅助验证");

        // 注意：完整的效用探测功能已整合到run_dynamic_behavior_test中
        // 这里只进行基本的效用指标快速检查

        let library_path = self
            .get_compiled_library_path(project_dir, language)
            .await?;
        let mut messages = Vec::new();
        let mut metrics = HashMap::new();

        // 快速库符号检查
        let symbol_check = tokio::task::spawn_blocking({
            let library_path = library_path.clone();
            move || {
                unsafe {
                    let lib = libloading::Library::new(&library_path)?;

                    // 检查核心符号是否存在
                    let has_init = lib
                        .get::<unsafe extern "C" fn()>(b"custom_mutator_init")
                        .or_else(|_| lib.get(b"init"))
                        .is_ok();
                    let has_mutate = lib
                        .get::<unsafe extern "C" fn()>(b"custom_mutator_mutate")
                        .or_else(|_| lib.get(b"mutate"))
                        .is_ok();

                    Ok::<(bool, bool), anyhow::Error>((has_init, has_mutate))
                }
            }
        })
        .await?;

        match symbol_check {
            Ok((has_init, has_mutate)) => {
                if has_init && has_mutate {
                    messages.push("✓ 核心变异器符号检查通过".to_string());
                    metrics.insert("symbol_completeness".to_string(), 1.0);
                } else {
                    messages.push(format!(
                        "⚠ 变异器符号不完整: init={has_init}, mutate={has_mutate}"
                    ));
                    metrics.insert("symbol_completeness".to_string(), 0.5);
                }
            }
            Err(e) => {
                messages.push(format!("✗ 库符号检查失败: {e}"));
                metrics.insert("symbol_completeness".to_string(), 0.0);
            }
        }

        // 基本库属性检查
        if let Ok(metadata) = tokio::fs::metadata(&library_path).await {
            let size_kb = metadata.len() as f32 / 1024.0;
            metrics.insert("library_size_kb".to_string(), size_kb);

            if size_kb > 0.0 && size_kb < 100_000.0 {
                // 合理的库大小范围
                messages.push(format!("✓ 库文件大小合理: {size_kb:.1}KB"));
            } else {
                messages.push(format!("⚠ 库文件大小异常: {size_kb:.1}KB"));
            }
        }

        messages.push("ℹ️  完整的效用探测已在动态行为测试中执行".to_string());

        // 简化的效用探测总是通过，实际验证在dynamic_behavior_test中进行
        Ok(ValidationResult {
            step: ValidationStep::UtilityProbe,
            passed: true,
            message: messages.join("\n"),
            metrics,
            duration: Duration::default(),
        })
    }

    /// 创建测试harness代码
    fn create_test_harness(&self, language: &str) -> String {
        match language {
            "rust" => r#"
use std::env;
use std::fs;
use std::process;

extern "C" {
    fn custom_mutator_init();
    fn custom_mutator_mutate(data: *mut u8, size: usize, max_size: usize) -> usize;
}

fn main() {
    let args: Vec<String> = env::args().collect();
    if args.len() != 2 {
        eprintln!("Usage: {} <seed_file>", args[0]);
        process::exit(1);
    }
    
    // 读取种子
    let mut data = fs::read(&args[1]).unwrap_or_else(|_| vec![0; 100]);
    data.resize(1024, 0); // 确保有足够空间
    
    unsafe {
        // 初始化变异器
        custom_mutator_init();
        
        // 执行变异
        let new_size = custom_mutator_mutate(
            data.as_mut_ptr(),
            data.len().min(100),
            data.len()
        );
        
        // 输出变异后的数据（用于熵计算）
        if new_size > 0 && new_size <= data.len() {
            print!("{:?}", &data[..new_size]);
        }
    }
}
"#
            .to_string(),

            "c" | "cpp" => r#"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

extern void custom_mutator_init();
extern size_t custom_mutator_mutate(uint8_t *data, size_t size, size_t max_size);

int main(int argc, char **argv) {
    if (argc != 2) {
        fprintf(stderr, "Usage: %s <seed_file>\n", argv[0]);
        return 1;
    }
    
    // 读取种子文件
    FILE *f = fopen(argv[1], "rb");
    if (!f) return 1;
    
    uint8_t data[1024] = {0};
    size_t size = fread(data, 1, 100, f);
    fclose(f);
    
    // 初始化变异器
    custom_mutator_init();
    
    // 执行变异
    size_t new_size = custom_mutator_mutate(data, size, sizeof(data));
    
    // 输出变异后的数据
    if (new_size > 0 && new_size <= sizeof(data)) {
        fwrite(data, 1, new_size, stdout);
    }
    
    return 0;
}
"#
            .to_string(),

            _ => String::new(),
        }
    }

    /// 创建测试用的Cargo.toml
    async fn create_test_cargo_toml(&self, test_dir: &Path, library_path: &Path) -> Result<()> {
        let cargo_toml = r#"[package]
name = "utility_test"
version = "0.1.0"
edition = "2021"

[dependencies]

[[bin]]
name = "utility_test"
path = "src/main.rs"

[profile.release]
lto = false
"#.to_string();

        tokio::fs::write(test_dir.join("Cargo.toml"), cargo_toml).await?;

        // 创建.cargo/config.toml来指定链接路径
        let cargo_dir = test_dir.join(".cargo");
        tokio::fs::create_dir_all(&cargo_dir).await?;

        let config_toml = format!(
            r#"[build]
rustflags = ["-L", "{}", "-l", "custom_mutator"]
"#,
            library_path.parent().unwrap().display()
        );

        tokio::fs::write(cargo_dir.join("config.toml"), config_toml).await?;

        Ok(())
    }

    /// 创建多样化测试种子集（workflow.md要求：短、长、结构化、二进制）
    fn create_diverse_test_seeds(&self) -> Vec<Vec<u8>> {
        vec![
            // 短种子
            vec![0; 1],             // 单字节零
            vec![0xFF],             // 单字节最大值
            vec![0x41, 0x42, 0x43], // 简单ASCII "ABC"
            b"Hi".to_vec(),         // 短文本
            // 中等长度种子
            vec![0; 10],                                          // 10字节零
            b"Hello, World!".to_vec(),                            // 标准ASCII文本
            (0..20).map(|i| i as u8).collect(),                   // 20字节递增
            vec![0xDE, 0xAD, 0xBE, 0xEF, 0xCA, 0xFE, 0xBA, 0xBE], // 常见二进制模式
            // 结构化数据种子
            b"{\"key\":\"value\"}".to_vec(),        // JSON结构
            b"<xml><tag>data</tag></xml>".to_vec(), // XML结构
            b"GET / HTTP/1.1\r\nHost: test\r\n\r\n".to_vec(), // HTTP请求结构
            [0x7F, 0x45, 0x4C, 0x46].to_vec(),      // ELF magic
            [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A].to_vec(), // PNG magic
            // 长种子
            vec![0xFF; 100],                                       // 100字节重复
            b"A".repeat(256),                                      // 256字节重复字符
            (0..255).cycle().take(500).map(|i| i as u8).collect(), // 500字节循环模式
            vec![rand::random::<u8>(); 1024],                      // 1KB随机数据
            // 二进制边界条件
            vec![0x00, 0x01, 0x7F, 0x80, 0xFF],  // 边界字节值
            (0..256).map(|i| i as u8).collect(), // 完整字节范围
            vec![0; 4096],                       // 4KB零块
            // 特殊模式
            [0xAA; 64].to_vec(), // 交替位模式
            [0x55; 64].to_vec(), // 反向交替位
            (0..64)
                .map(|i| if i % 2 == 0 { 0x00 } else { 0xFF })
                .collect(), // 零一交替
        ]
    }


    /// 计算输出熵
    fn calculate_entropy(&self, outputs: &std::collections::HashSet<Vec<u8>>) -> f32 {
        if outputs.is_empty() {
            return 0.0;
        }

        // 统计所有字节的频率
        let mut byte_counts = [0u64; 256];
        let mut total_bytes = 0u64;

        for output in outputs {
            for &byte in output {
                byte_counts[byte as usize] += 1;
                total_bytes += 1;
            }
        }

        // 计算香农熵
        let mut entropy = 0.0f32;
        for count in byte_counts {
            if count > 0 {
                let probability = count as f32 / total_bytes as f32;
                entropy -= probability * probability.log2();
            }
        }

        entropy
    }

    /// 获取编译产物路径
    async fn get_compiled_library_path(
        &self,
        project_dir: &Path,
        language: &str,
    ) -> Result<PathBuf> {
        match language {
            "rust" => {
                let target_dir = project_dir.join("target/release");
                let lib_name = if cfg!(target_os = "windows") {
                    "custom_mutator.dll"
                } else if cfg!(target_os = "macos") {
                    "libcustom_mutator.dylib"
                } else {
                    "libcustom_mutator.so"
                };
                Ok(target_dir.join(lib_name))
            }
            "c" | "cpp" => Ok(project_dir.join("mutator.so")),
            _ => Err(anyhow!("不支持的语言: {}", language)),
        }
    }

    /// 运行命令
    async fn run_command(
        &self,
        command: &str,
        args: &[&str],
        cwd: &Path,
    ) -> Result<std::process::Output> {
        let output = timeout(
            self.config.timeout_per_step,
            tokio::process::Command::new(command)
                .args(args)
                .current_dir(cwd)
                .output(),
        )
        .await??;

        Ok(output)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_validation_sandbox() {
        let config = ValidationConfig::default();
        let sandbox = CompoundValidationSandbox::new(config).unwrap();

        let test_code = r#"
use libafl::mutators::Mutator;

pub struct CustomMutator;

impl<I> Mutator<I> for CustomMutator {
    fn mutate(&mut self, _state: &mut S, input: &mut I) -> Result<MutationResult, Error> {
        Ok(MutationResult::Mutated)
    }
}
"#;

        let (passed, results, _) = sandbox
            .validate_code(
                test_code,
                "rust",
                vec![ValidationStep::StaticAnalysis, ValidationStep::Compilation],
            )
            .await
            .unwrap();

        println!("验证结果: {}", if passed { "通过" } else { "失败" });
        for result in results {
            println!(
                "  {:?}: {} - {}",
                result.step,
                if result.passed { "✓" } else { "✗" },
                result.message
            );
        }
    }
}