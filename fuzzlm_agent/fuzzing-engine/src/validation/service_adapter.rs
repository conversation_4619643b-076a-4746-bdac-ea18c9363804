/*!
验证服务适配器
负责gRPC协议与CompoundValidationSandbox业务逻辑之间的类型转换
遵循单一职责原则，将网络协议层与业务逻辑层完全解耦
*/

use anyhow::Result;
use log::{error, info};
use std::sync::Arc;
use tokio::sync::Mutex;
use tonic::Status;

use crate::validation::sandbox::{
    CompoundValidationSandbox, ValidationStep as SandboxValidationStep,
};

// 导入生成的protobuf类型
use crate::grpc_server::fuzzing_control::{
    ValidateCodeRequest, ValidateCodeResponse, ValidationResult,
};

/// 验证服务适配器
///
/// 职责：
/// 1. 将gRPC请求转换为业务逻辑参数
/// 2. 调用CompoundValidationSandbox执行验证
/// 3. 将业务逻辑结果转换为gRPC响应
/// 4. 统一错误处理和日志记录
pub struct ValidationServiceAdapter {
    validation_sandbox: Arc<Mutex<CompoundValidationSandbox>>,
}

impl ValidationServiceAdapter {
    /// 创建新的验证服务适配器
    pub fn new(validation_sandbox: Arc<Mutex<CompoundValidationSandbox>>) -> Self {
        Self { validation_sandbox }
    }

    /// 执行代码验证
    ///
    /// 这是适配器的核心方法，负责完整的请求-响应流程：
    /// 1. 解析和验证gRPC请求参数
    /// 2. 转换为业务逻辑所需的类型
    /// 3. 调用验证沙箱执行验证
    /// 4. 转换和格式化返回结果
    pub async fn validate_code(
        &self,
        request: ValidateCodeRequest,
    ) -> Result<ValidateCodeResponse, Status> {
        info!("开始处理代码验证请求，语言: {}", request.language);

        // 1. 转换请求参数
        let language = &request.language;
        let sandbox_steps = self.convert_validation_steps(&request.validation_steps)?;

        // 2. 调用业务逻辑
        let sandbox = self.validation_sandbox.lock().await;
        let validation_result = sandbox
            .validate_code(&request.code, language, sandbox_steps)
            .await;

        // 3. 处理业务逻辑返回结果
        match validation_result {
            Ok((success, sandbox_results, compiled_path)) => {
                info!("验证完成，结果: {}", if success { "成功" } else { "失败" });

                // 4. 转换为gRPC响应格式
                let grpc_results = self.convert_validation_results(sandbox_results)?;

                let response = ValidateCodeResponse {
                    passed: success,
                    results: grpc_results,
                    compiled_path: compiled_path
                        .map(|p| p.to_string_lossy().to_string())
                        .unwrap_or_default(),
                };

                Ok(response)
            }
            Err(e) => {
                error!("验证执行失败: {e}");
                Err(Status::internal(format!("验证执行失败: {e}")))
            }
        }
    }

    /// 转换验证步骤：protobuf字符串数组 -> 业务逻辑枚举数组
    #[allow(clippy::result_large_err)]
    fn convert_validation_steps(
        &self,
        steps: &[String],
    ) -> Result<Vec<SandboxValidationStep>, Status> {
        let mut sandbox_steps = Vec::new();

        for step in steps {
            let sandbox_step = match step.as_str() {
                "static_analysis" | "StaticAnalysis" => SandboxValidationStep::StaticAnalysis,
                "compilation" | "Compilation" => SandboxValidationStep::Compilation,
                "dynamic_behavior" | "DynamicBehavior" => SandboxValidationStep::DynamicBehavior,
                "utility_probe" | "UtilityProbe" => SandboxValidationStep::UtilityProbe,
                _ => return Err(Status::invalid_argument(format!("未知的验证步骤: {step}"))),
            };

            sandbox_steps.push(sandbox_step);
        }

        Ok(sandbox_steps)
    }

    /// 转换验证结果：业务逻辑结果数组 -> protobuf结果数组
    #[allow(clippy::result_large_err)]
    fn convert_validation_results(
        &self,
        sandbox_results: Vec<crate::validation::sandbox::ValidationResult>,
    ) -> Result<Vec<ValidationResult>, Status> {
        let mut grpc_results = Vec::new();

        for sandbox_result in sandbox_results {
            let step_name = match sandbox_result.step {
                SandboxValidationStep::StaticAnalysis => "static_analysis",
                SandboxValidationStep::Compilation => "compilation", 
                SandboxValidationStep::DynamicBehavior => "dynamic_behavior",
                SandboxValidationStep::UtilityProbe => "utility_probe",
            };

            let grpc_result = ValidationResult {
                step: step_name.to_string(),
                passed: sandbox_result.passed,
                message: sandbox_result.message,
                metrics: sandbox_result.metrics,
            };

            grpc_results.push(grpc_result);
        }

        Ok(grpc_results)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::validation::sandbox::{CompoundValidationSandbox, ValidationConfig};

    fn create_test_adapter() -> ValidationServiceAdapter {
        let config = ValidationConfig::default();
        let sandbox = CompoundValidationSandbox::new(config).unwrap();
        let sandbox_arc = Arc::new(Mutex::new(sandbox));
        ValidationServiceAdapter::new(sandbox_arc)
    }

    #[test]
    fn test_convert_validation_steps() {
        let adapter = create_test_adapter();

        let steps = vec![
            "static_analysis".to_string(),
            "compilation".to_string(),
        ];

        let result = adapter.convert_validation_steps(&steps).unwrap();
        assert_eq!(result.len(), 2);
        assert_eq!(result[0], SandboxValidationStep::StaticAnalysis);
        assert_eq!(result[1], SandboxValidationStep::Compilation);
    }

    #[test]
    fn test_convert_validation_steps_invalid() {
        let adapter = create_test_adapter();

        let steps = vec!["unknown_step".to_string()]; // 无效的步骤值
        let result = adapter.convert_validation_steps(&steps);

        assert!(result.is_err());
        assert!(result.unwrap_err().message().contains("未知的验证步骤"));
    }
}