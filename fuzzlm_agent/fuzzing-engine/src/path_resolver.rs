/*!
智能路径解析器模块

用于解决硬编码路径问题，提供灵活的路径发现机制：
1. 优先使用环境变量配置
2. 自动探测相对路径
3. 提供多级后备路径
*/

use anyhow::{anyhow, Result};
use log::{debug, info, warn};
use std::env;
use std::path::{Path, PathBuf};

/// 路径解析器，负责将硬编码路径转换为动态路径发现
pub struct PathResolver {
    project_root: Option<PathBuf>,
    libafl_path: Option<PathBuf>,
    engine_path: Option<PathBuf>,
}

impl PathResolver {
    /// 创建新的路径解析器
    pub fn new() -> Self {
        Self {
            project_root: None,
            libafl_path: None,
            engine_path: None,
        }
    }

    /// 创建带有缓存的路径解析器（推荐使用）
    pub fn with_cache() -> Self {
        let mut resolver = Self::new();
        resolver.initialize_cache();
        resolver
    }

    /// 初始化路径缓存
    fn initialize_cache(&mut self) {
        self.project_root = Self::find_project_root_internal();
        self.libafl_path = Self::find_libafl_path_internal();
        self.engine_path = Self::find_engine_path_internal();
    }

    /// 获取项目根目录
    pub fn get_project_root(&self) -> Result<PathBuf> {
        if let Some(ref path) = self.project_root {
            return Ok(path.clone());
        }
        Self::find_project_root()
    }

    /// 获取 LibAFL 库路径
    pub fn get_libafl_path(&self) -> Result<PathBuf> {
        if let Some(ref path) = self.libafl_path {
            return Ok(path.clone());
        }
        Self::find_libafl_path()
    }

    /// 获取引擎二进制路径
    pub fn get_engine_path(&self) -> Result<PathBuf> {
        if let Some(ref path) = self.engine_path {
            return Ok(path.clone());
        }
        Self::find_engine_path()
    }

    /// 查找项目根目录
    pub fn find_project_root() -> Result<PathBuf> {
        Self::find_project_root_internal().ok_or_else(|| anyhow!("无法找到项目根目录"))
    }

    fn find_project_root_internal() -> Option<PathBuf> {
        // 1. 优先使用环境变量
        if let Ok(root) = env::var("FUZZLM_PROJECT_ROOT") {
            let path = PathBuf::from(root);
            if path.exists() && Self::is_project_root(&path) {
                info!("使用环境变量 FUZZLM_PROJECT_ROOT: {path:?}");
                return Some(path);
            }
        }

        // 2. 从当前目录向上查找
        let mut current = env::current_dir().ok()?;
        loop {
            if Self::is_project_root(&current) {
                debug!("通过向上搜索找到项目根目录: {current:?}");
                return Some(current);
            }

            if !current.pop() {
                break;
            }
        }

        // 3. 尝试相对路径
        let relative_candidates = vec![".", "..", "../..", "../../.."];

        for candidate in relative_candidates {
            let path = PathBuf::from(candidate);
            if Self::is_project_root(&path) {
                debug!("通过相对路径找到项目根目录: {path:?}");
                return path.canonicalize().ok();
            }
        }

        warn!("无法找到项目根目录");
        None
    }

    /// 检查是否为项目根目录
    fn is_project_root(path: &Path) -> bool {
        // 检查关键文件存在
        let key_files = vec![
            "fuzzlm_agent.txt",
            "CLAUDE.md",
            "requirements.txt",
            "fuzzlm_agent/fuzzing-engine/Cargo.toml",
        ];

        for file in &key_files {
            if path.join(file).exists() {
                return true;
            }
        }

        false
    }

    /// 查找 LibAFL 路径
    pub fn find_libafl_path() -> Result<PathBuf> {
        Self::find_libafl_path_internal().ok_or_else(|| anyhow!("无法找到 LibAFL 路径"))
    }

    fn find_libafl_path_internal() -> Option<PathBuf> {
        // 1. 优先使用环境变量
        if let Ok(libafl_path) = env::var("FUZZLM_LIBAFL_PATH") {
            let path = PathBuf::from(libafl_path);
            if path.exists() && Self::is_libafl_directory(&path) {
                info!("使用环境变量 FUZZLM_LIBAFL_PATH: {path:?}");
                return Some(path);
            }
        }

        // 2. 基于项目根目录查找
        if let Some(project_root) = Self::find_project_root_internal() {
            let libafl_path = project_root.join("LibAFL");
            if libafl_path.exists() && Self::is_libafl_directory(&libafl_path) {
                debug!("在项目根目录找到 LibAFL: {libafl_path:?}");
                return Some(libafl_path);
            }
        }

        // 3. 相对路径候选
        let candidates = vec![
            "./LibAFL",
            "../LibAFL",
            "../../LibAFL",
            "./LibAFL/libafl",
            "../LibAFL/libafl",
            "../../LibAFL/libafl",
        ];

        for candidate in candidates {
            let path = PathBuf::from(candidate);
            if path.exists() && Self::is_libafl_directory(&path) {
                debug!("通过相对路径找到 LibAFL: {candidate:?}");
                return path.canonicalize().ok();
            }
        }

        warn!("无法找到 LibAFL 路径");
        None
    }

    /// 检查是否为 LibAFL 目录
    fn is_libafl_directory(path: &Path) -> bool {
        // 检查 LibAFL 关键文件
        let key_files = vec![
            "libafl/Cargo.toml",
            "libafl_bolts/Cargo.toml",
            "libafl_targets/Cargo.toml",
        ];

        for file in &key_files {
            if path.join(file).exists() {
                return true;
            }
        }

        // 如果路径直接是 libafl 子目录
        if path.file_name() == Some(std::ffi::OsStr::new("libafl")) {
            return path.join("Cargo.toml").exists();
        }

        false
    }

    /// 查找引擎二进制路径
    pub fn find_engine_path() -> Result<PathBuf> {
        Self::find_engine_path_internal().ok_or_else(|| anyhow!("无法找到引擎二进制路径"))
    }

    fn find_engine_path_internal() -> Option<PathBuf> {
        // 1. 优先使用环境变量
        if let Ok(engine_path) = env::var("FUZZLM_ENGINE_PATH") {
            let path = PathBuf::from(engine_path);
            if path.exists() {
                info!("使用环境变量 FUZZLM_ENGINE_PATH: {path:?}");
                return Some(path);
            }
        }

        // 2. 基于项目根目录查找
        if let Some(project_root) = Self::find_project_root_internal() {
            let candidates = vec![
                project_root.join("fuzzlm_agent/fuzzing-engine/target/release/fuzzing-engine"),
                project_root.join("fuzzlm_agent/fuzzing-engine/target/debug/fuzzing-engine"),
                project_root.join("fuzzlm_agent/fuzzing-engine/target/release/libafl-harness"),
                project_root.join("fuzzlm_agent/fuzzing-engine/target/debug/libafl-harness"),
            ];

            for candidate in candidates {
                if candidate.exists() {
                    debug!("在项目目录找到引擎二进制: {candidate:?}");
                    return Some(candidate);
                }
            }
        }

        // 3. 相对路径候选
        let candidates = vec![
            "./target/release/fuzzing-engine",
            "./target/debug/fuzzing-engine",
            "./target/release/libafl-harness",
            "./target/debug/libafl-harness",
            "../target/release/fuzzing-engine",
            "../target/debug/fuzzing-engine",
            "../target/release/libafl-harness",
            "../target/debug/libafl-harness",
        ];

        for candidate in candidates {
            let path = PathBuf::from(candidate);
            if path.exists() {
                debug!("通过相对路径找到引擎二进制: {candidate:?}");
                return path.canonicalize().ok();
            }
        }

        warn!("无法找到引擎二进制路径");
        None
    }

    /// 查找 libafl_fuzzbench 目录
    pub fn find_fuzzbench_dir() -> Result<PathBuf> {
        // 1. 优先使用环境变量
        if let Ok(fuzzbench_path) = env::var("FUZZLM_FUZZBENCH_PATH") {
            let path = PathBuf::from(fuzzbench_path);
            if path.exists() && path.is_dir() {
                info!("使用环境变量 FUZZLM_FUZZBENCH_PATH: {path:?}");
                return Ok(path);
            }
        }

        // 2. 基于项目根目录查找
        if let Ok(project_root) = Self::find_project_root() {
            let fuzzbench_path = project_root.join("libafl_fuzzbench");
            if fuzzbench_path.exists() && fuzzbench_path.is_dir() {
                debug!("在项目根目录找到 libafl_fuzzbench: {fuzzbench_path:?}");
                return Ok(fuzzbench_path);
            }
        }

        // 3. 相对路径候选
        let candidates = vec![
            "./libafl_fuzzbench",
            "../libafl_fuzzbench",
            "../../libafl_fuzzbench",
            "./libafl_fuzzbench",
        ];

        for candidate in candidates {
            let path = PathBuf::from(candidate);
            if path.exists() && path.is_dir() {
                debug!("通过相对路径找到 libafl_fuzzbench: {candidate:?}");
                return Ok(path.canonicalize()?);
            }
        }

        // 4. 默认路径
        Ok(PathBuf::from("./libafl_fuzzbench"))
    }

    /// 获取 LibAFL 组件路径（用于 Cargo.toml 生成）
    pub fn get_libafl_components(&self) -> Result<LibAFLComponents> {
        let libafl_root = self.get_libafl_path()?;

        Ok(LibAFLComponents {
            libafl: libafl_root.join("libafl"),
            libafl_bolts: libafl_root.join("libafl_bolts"),
            libafl_targets: libafl_root.join("libafl_targets"),
        })
    }

    /// 静态方法版本，直接获取 LibAFL 组件路径
    pub fn get_libafl_components_static() -> Result<LibAFLComponents> {
        let resolver = Self::with_cache();
        resolver.get_libafl_components()
    }
}

impl Default for PathResolver {
    fn default() -> Self {
        Self::with_cache()
    }
}

/// LibAFL 组件路径
#[derive(Debug, Clone)]
pub struct LibAFLComponents {
    pub libafl: PathBuf,
    pub libafl_bolts: PathBuf,
    pub libafl_targets: PathBuf,
}

impl LibAFLComponents {
    /// 验证所有组件路径是否存在
    pub fn validate(&self) -> Result<()> {
        let components = vec![
            ("libafl", &self.libafl),
            ("libafl_bolts", &self.libafl_bolts),
            ("libafl_targets", &self.libafl_targets),
        ];

        for (name, path) in components {
            if !path.exists() {
                return Err(anyhow!("LibAFL 组件 {} 不存在: {:?}", name, path));
            }
        }

        Ok(())
    }

    /// 生成用于 Cargo.toml 的路径字符串
    pub fn to_cargo_paths(&self) -> Result<(String, String, String)> {
        self.validate()?;

        Ok((
            self.libafl.to_string_lossy().to_string(),
            self.libafl_bolts.to_string_lossy().to_string(),
            self.libafl_targets.to_string_lossy().to_string(),
        ))
    }
}

