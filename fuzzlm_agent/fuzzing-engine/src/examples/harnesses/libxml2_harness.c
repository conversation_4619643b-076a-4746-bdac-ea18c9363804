/*
 * LibAFL兼容的libxml2 XML模糊测试目标
 * 基于FuzzBench的libxml2_xml benchmark改造
 * 修复版本：移除AFL覆盖率代码冲突，使用LibAFL的覆盖率系统
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

// LibXML2头文件
#include <libxml/xmlversion.h>
#include <libxml/parser.h>
#include <libxml/tree.h>
#include <libxml/HTMLparser.h>

// LibXML2错误处理器（静默）
void ignore_error(void *ctx, const char *msg, ...) {
    // 忽略错误消息以提高性能
    (void)ctx;  // 避免未使用参数警告
    (void)msg;  // 避免未使用参数警告
}

// 主要的fuzzing目标函数
int fuzz_libxml2(const uint8_t *data, size_t size) {
    if (size == 0 || size > 1024 * 1024) {
        return 0; // 跳过空数据或过大数据
    }

    // 设置libxml2错误处理
    xmlSetGenericErrorFunc(NULL, &ignore_error);
    xmlSetStructuredErrorFunc(NULL, NULL);

    // 创建以null结尾的字符串
    char *xml_data = (char*)malloc(size + 1);
    if (!xml_data) {
        return 0;
    }
    memcpy(xml_data, data, size);
    xml_data[size] = '\0';

    // 解析XML文档
    xmlDocPtr doc = xmlReadMemory(xml_data, size, "fuzzed.xml", NULL, 
        XML_PARSE_RECOVER | XML_PARSE_NOENT | XML_PARSE_DTDLOAD);

    if (doc) {
        // 对解析成功的文档进行一些操作
        xmlNodePtr root = xmlDocGetRootElement(doc);
        if (root) {
            // 遍历节点以增加代码覆盖
            xmlNodePtr cur = root;
            while (cur) {
                if (cur->name) {
                    // 触发字符串比较逻辑
                    if (xmlStrcmp(cur->name, (const xmlChar*)"root") == 0) {
                        // 特殊处理root节点
                    }
                    if (xmlStrcmp(cur->name, (const xmlChar*)"test") == 0) {
                        // 触发特定路径
                    }
                }
                
                // 检查属性
                xmlAttrPtr attr = cur->properties;
                while (attr) {
                    if (attr->children && attr->children->content) {
                        // 处理属性值
                        const char* value = (const char*)attr->children->content;
                        if (strstr(value, "crash")) {
                            // 潜在的崩溃触发点
                        }
                    }
                    attr = attr->next;
                }
                
                cur = cur->next;
            }
        }
        
        xmlFreeDoc(doc);
    }

    free(xml_data);
    xmlCleanupParser();
    return 0;
}

#ifdef __cplusplus
extern "C" {
#endif

// LibAFL标准接口 - 提供强符号覆盖LibAFL弱符号
int LLVMFuzzerTestOneInput(const uint8_t *data, size_t size) {
    return fuzz_libxml2(data, size);
}

// LibAFL直接调用接口 - 保持向后兼容
int libafl_libxml2_target(const uint8_t *data, size_t size) {
    return fuzz_libxml2(data, size);
}

#ifdef __cplusplus
}
#endif