/*!
指标收集模块

收集和管理 Fuzzing 过程中的各种性能指标和统计数据
*/

use crate::error::{EngineError, Result};

use chrono::{DateTime, Utc};
use log::debug;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 覆盖率统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoverageStats {
    /// 总基本块数量
    pub total_blocks: u64,
    /// 已覆盖的基本块数量
    pub covered_blocks: u64,
    /// 覆盖率百分比
    pub coverage_percentage: f64,
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// 每秒执行次数
    pub executions_per_second: f64,
    /// 总执行次数
    pub total_executions: u64,
}

/// 状态数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusData {
    /// 会话 ID
    pub session_id: String,
    /// 会话状态
    pub state: String,
    /// 覆盖率统计
    pub coverage_stats: CoverageStats,
    /// 性能指标
    pub performance_metrics: PerformanceMetrics,
    /// 崩溃数量
    pub crash_count: u64,
    /// 执行次数
    pub execution_count: u64,
    /// 运行时间（秒）
    pub runtime_seconds: f64,
}

/// 会话指标数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionMetrics {
    /// 会话 ID
    pub session_id: String,
    /// 会话状态
    pub state: SessionState,
    /// 开始时间
    pub start_time: DateTime<Utc>,
    /// 最后更新时间
    pub last_update: DateTime<Utc>,
    /// 总执行次数
    pub total_executions: u64,
    /// 覆盖的基本块数量
    pub covered_blocks: u64,
    /// 总基本块数量
    pub total_blocks: u64,
    /// 发现的崩溃数量
    pub crash_count: u64,
    /// 当前执行速度（每秒执行次数）
    pub exec_per_sec: f64,
    /// 语料库大小
    pub corpus_size: u64,
    /// 内存使用量（字节）
    pub memory_usage: u64,
    /// CPU 使用率（百分比）
    pub cpu_usage: f64,
}

/// 会话状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SessionState {
    /// 初始化中
    Initializing,
    /// 运行中
    Running,
    /// 暂停
    Paused,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
}

impl SessionState {
    pub fn as_str(&self) -> &str {
        match self {
            SessionState::Initializing => "initializing",
            SessionState::Running => "running",
            SessionState::Paused => "paused",
            SessionState::Stopped => "stopped",
            SessionState::Error(_) => "error",
        }
    }
}

/// 指标收集器
pub struct MetricsCollector {
    /// 会话指标映射
    session_metrics: HashMap<String, SessionMetrics>,
    /// 系统信息收集器
    system_info: sysinfo::System,
}

impl Default for MetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}

impl MetricsCollector {
    /// 创建新的指标收集器
    pub fn new() -> Self {
        Self {
            session_metrics: HashMap::new(),
            system_info: sysinfo::System::new_all(),
        }
    }

    /// 初始化会话指标
    pub fn initialize_session(&mut self, session_id: String) -> Result<()> {
        let now = Utc::now();
        let metrics = SessionMetrics {
            session_id: session_id.clone(),
            state: SessionState::Initializing,
            start_time: now,
            last_update: now,
            total_executions: 0,
            covered_blocks: 0,
            total_blocks: 1000, // 默认值，后续会更新
            crash_count: 0,
            exec_per_sec: 0.0,
            corpus_size: 0,
            memory_usage: 0,
            cpu_usage: 0.0,
        };

        self.session_metrics.insert(session_id.clone(), metrics);
        debug!("初始化会话 {session_id} 的指标");
        Ok(())
    }

    /// 更新会话指标
    pub fn update_session_metrics(
        &mut self,
        session_id: &str,
        updates: SessionMetricsUpdate,
    ) -> Result<()> {
        {
            let metrics = self.session_metrics.get_mut(session_id).ok_or_else(|| {
                EngineError::internal(format!("会话 {session_id} 的指标不存在"))
            })?;

            // 更新时间戳
            metrics.last_update = Utc::now();

            // 应用更新
            if let Some(state) = updates.state {
                metrics.state = state;
            }
            if let Some(executions) = updates.total_executions {
                metrics.total_executions = executions;
            }
            if let Some(covered) = updates.covered_blocks {
                metrics.covered_blocks = covered;
            }
            if let Some(total) = updates.total_blocks {
                metrics.total_blocks = total;
            }
            if let Some(crashes) = updates.crash_count {
                metrics.crash_count = crashes;
            }
            if let Some(speed) = updates.exec_per_sec {
                metrics.exec_per_sec = speed;
            }
            if let Some(size) = updates.corpus_size {
                metrics.corpus_size = size;
            }
        }

        // 更新系统资源使用情况
        self.update_system_metrics_for_session(session_id);

        debug!("更新会话 {session_id} 的指标");
        Ok(())
    }

    /// 获取会话指标
    pub fn get_session_metrics(&self, session_id: &str) -> Result<&SessionMetrics> {
        self.session_metrics
            .get(session_id)
            .ok_or_else(|| EngineError::internal(format!("会话 {session_id} 的指标不存在")))
    }

    /// 移除会话指标
    pub fn remove_session(&mut self, session_id: &str) -> Result<()> {
        self.session_metrics.remove(session_id);
        debug!("移除会话 {session_id} 的指标");
        Ok(())
    }

    /// 获取所有活跃会话
    pub fn get_active_sessions(&self) -> Vec<String> {
        self.session_metrics.keys().cloned().collect()
    }

    /// 生成状态数据
    pub async fn get_status_data(
        &self,
        session_id: &str,
        metrics: &SessionMetrics,
    ) -> Result<StatusData> {
        let runtime_seconds = (Utc::now() - metrics.start_time).num_seconds() as f64;

        let coverage_percentage = if metrics.total_blocks > 0 {
            (metrics.covered_blocks as f64 / metrics.total_blocks as f64) * 100.0
        } else {
            0.0
        };

        Ok(StatusData {
            session_id: session_id.to_string(),
            state: metrics.state.as_str().to_string(),
            coverage_stats: CoverageStats {
                total_blocks: metrics.total_blocks,
                covered_blocks: metrics.covered_blocks,
                coverage_percentage,
            },
            performance_metrics: PerformanceMetrics {
                executions_per_second: metrics.exec_per_sec,
                total_executions: metrics.total_executions,
            },
            crash_count: metrics.crash_count,
            execution_count: metrics.total_executions,
            runtime_seconds,
        })
    }

    /// 更新系统指标
    #[allow(dead_code)]
    fn update_system_metrics(&mut self, metrics: &mut SessionMetrics) {
        self.system_info.refresh_all();

        // 获取当前进程的内存和 CPU 使用情况
        if let Some(process) = self
            .system_info
            .process(sysinfo::get_current_pid().unwrap())
        {
            metrics.memory_usage = process.memory();
            metrics.cpu_usage = process.cpu_usage() as f64;
        }
    }

    /// 为特定会话更新系统指标
    fn update_system_metrics_for_session(&mut self, session_id: &str) {
        self.system_info.refresh_all();

        if let Some(metrics) = self.session_metrics.get_mut(session_id) {
            // 获取当前进程的内存和 CPU 使用情况
            if let Some(process) = self
                .system_info
                .process(sysinfo::get_current_pid().unwrap())
            {
                metrics.memory_usage = process.memory();
                metrics.cpu_usage = process.cpu_usage() as f64;
            }
        }
    }
}

/// 会话指标更新结构
#[derive(Debug, Default)]
pub struct SessionMetricsUpdate {
    pub state: Option<SessionState>,
    pub total_executions: Option<u64>,
    pub covered_blocks: Option<u64>,
    pub total_blocks: Option<u64>,
    pub crash_count: Option<u64>,
    pub exec_per_sec: Option<f64>,
    pub corpus_size: Option<u64>,
}

impl SessionMetricsUpdate {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn with_state(mut self, state: SessionState) -> Self {
        self.state = Some(state);
        self
    }

    pub fn with_executions(mut self, executions: u64) -> Self {
        self.total_executions = Some(executions);
        self
    }

    pub fn with_coverage(mut self, covered: u64, total: u64) -> Self {
        self.covered_blocks = Some(covered);
        self.total_blocks = Some(total);
        self
    }

    pub fn with_crashes(mut self, crashes: u64) -> Self {
        self.crash_count = Some(crashes);
        self
    }

    pub fn with_speed(mut self, speed: f64) -> Self {
        self.exec_per_sec = Some(speed);
        self
    }

    pub fn with_corpus_size(mut self, size: u64) -> Self {
        self.corpus_size = Some(size);
        self
    }
}
