/*!
Phase 4.1: 高性能遥测数据发送模块
专为高频fuzzing数据优化的批量发送和缓冲机制
*/

use anyhow::{anyhow, Result};
use log::{debug, info, warn};
use std::sync::Arc;
use std::time::{Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, Duration};

use crate::telemetry_producer::{TelemetryDataType, TelemetryEntry, TelemetryProducer};

/// 高性能遥测数据缓冲器 - 专为高频数据优化
pub struct HighPerformanceTelemetryBuffer {
    max_buffer_size: usize,
    #[allow(dead_code)]
    batch_size: usize,
    #[allow(dead_code)]
    flush_interval_ms: u64,
    telemetry_producer: Arc<TelemetryProducer>,
    sender: mpsc::UnboundedSender<TelemetryEntry>,
    stats: Arc<RwLock<BufferStats>>,
}

#[derive(Debug, Default, <PERSON><PERSON>)]
pub struct BufferStats {
    pub entries_buffered: u64,
    pub entries_sent: u64,
    pub batches_sent: u64,
    pub buffer_overflows: u64,
    pub send_errors: u64,
    pub avg_batch_size: f64,
    pub last_flush_time: Option<SystemTime>,
    pub buffer_utilization: f64,
}

impl HighPerformanceTelemetryBuffer {
    /// 创建高性能遥测缓冲器
    pub async fn new(
        telemetry_producer: Arc<TelemetryProducer>,
        max_buffer_size: usize,
        batch_size: usize,
        flush_interval_ms: u64,
    ) -> Result<Arc<Self>> {
        let stats = Arc::new(RwLock::new(BufferStats::default()));

        // 创建异步发送通道
        let (sender, mut receiver) = mpsc::unbounded_channel::<TelemetryEntry>();

        let stats_clone = Arc::clone(&stats);

        // 创建self的Arc引用
        let buffer = Arc::new(Self {
            max_buffer_size,
            batch_size,
            flush_interval_ms,
            telemetry_producer,
            sender,
            stats,
        });

        let buffer_clone = Arc::clone(&buffer);

        // 启动批量发送任务
        tokio::spawn(async move {
            Self::batch_sender_task(
                stats_clone,
                buffer_clone,
                batch_size,
                flush_interval_ms,
                &mut receiver,
            )
            .await;
        });

        info!(
            "✓ 高性能遥测缓冲器初始化完成 - 缓冲区: {max_buffer_size}, 批量: {batch_size}, 刷新间隔: {flush_interval_ms}ms"
        );

        Ok(buffer)
    }

    /// 批量发送任务 - 在后台异步处理
    async fn batch_sender_task(
        stats: Arc<RwLock<BufferStats>>,
        buffer: Arc<HighPerformanceTelemetryBuffer>,
        batch_size: usize,
        flush_interval_ms: u64,
        receiver: &mut mpsc::UnboundedReceiver<TelemetryEntry>,
    ) {
        let mut flush_timer = interval(Duration::from_millis(flush_interval_ms));
        let mut pending_entries = Vec::with_capacity(batch_size);

        info!("启动高性能遥测批量发送任务");

        loop {
            tokio::select! {
                // 接收新的遥测条目
                entry_opt = receiver.recv() => {
                    match entry_opt {
                        Some(entry) => {
                            pending_entries.push(entry);

                            // 缓冲区满时立即发送
                            if pending_entries.len() >= batch_size {
                                Self::flush_batch(&pending_entries, &buffer, &stats).await;
                                pending_entries.clear();
                            }
                        }
                        None => {
                            warn!("遥测发送通道关闭，退出批量发送任务");
                            break;
                        }
                    }
                }

                // 定时刷新
                _ = flush_timer.tick() => {
                    if !pending_entries.is_empty() {
                        Self::flush_batch(&pending_entries, &buffer, &stats).await;
                        pending_entries.clear();
                    }
                }
            }
        }

        // 最后刷新剩余数据
        if !pending_entries.is_empty() {
            Self::flush_batch(&pending_entries, &buffer, &stats).await;
        }

        info!("高性能遥测批量发送任务退出");
    }

    /// 刷新批量数据
    async fn flush_batch(
        entries: &[TelemetryEntry],
        buffer: &HighPerformanceTelemetryBuffer,
        stats: &Arc<RwLock<BufferStats>>,
    ) {
        if entries.is_empty() {
            return;
        }

        let batch_size = entries.len();
        let start_time = Instant::now();
        let mut success_count = 0;

        // 批量发送所有条目
        for entry in entries {
            // 使用高性能路径发送
            match buffer.write_entry_direct(entry.clone()).await {
                Ok(_) => success_count += 1,
                Err(e) => {
                    debug!("遥测条目发送失败: {:?} - {}", entry.data_type, e);
                }
            }
        }

        let send_duration = start_time.elapsed();

        // 更新统计信息
        {
            let mut stats_guard = stats.write().await;
            stats_guard.batches_sent += 1;
            stats_guard.entries_sent += success_count as u64;
            if success_count < batch_size {
                stats_guard.send_errors += (batch_size - success_count) as u64;
            }
            stats_guard.last_flush_time = Some(SystemTime::now());

            // 更新平均批量大小
            let total_batches = stats_guard.batches_sent as f64;
            stats_guard.avg_batch_size = (stats_guard.avg_batch_size * (total_batches - 1.0)
                + batch_size as f64)
                / total_batches;
        }

        if success_count == batch_size {
            debug!(
                "批量发送成功: {} 条目, 耗时: {:.2}ms",
                batch_size,
                send_duration.as_millis()
            );
        } else {
            warn!(
                "批量发送部分失败: {}/{} 成功, 耗时: {:.2}ms",
                success_count,
                batch_size,
                send_duration.as_millis()
            );
        }
    }

    /// 高性能发送遥测条目 - 异步非阻塞
    pub async fn send_entry(&self, entry: TelemetryEntry) -> Result<()> {
        // 更新缓冲区统计
        {
            let mut stats_guard = self.stats.write().await;
            stats_guard.entries_buffered += 1;

            // 生产级溢出检查 - 基于通道容量和性能指标
            let current_buffered = stats_guard.entries_buffered - stats_guard.entries_sent;
            if current_buffered > self.max_buffer_size as u64 {
                stats_guard.buffer_overflows += 1;
                warn!("遥测缓冲区估计溢出! 积压条目: {current_buffered}");
                // 不返回错误，允许继续发送以避免丢失重要数据
            }

            stats_guard.buffer_utilization = current_buffered as f64 / self.max_buffer_size as f64;
        }

        // 通过异步通道发送（非阻塞）
        self.sender
            .send(entry)
            .map_err(|e| anyhow!("发送遥测条目到通道失败: {}", e))?;

        Ok(())
    }

    /// 快速发送执行统计 - 高频调用优化
    pub async fn send_execution_stats_fast(
        &self,
        instance_id: &str,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    ) -> Result<()> {
        // 预分配数据缓冲区避免重新分配
        let mut data = Vec::with_capacity(16);
        data.extend_from_slice(&executions.to_le_bytes());
        data.extend_from_slice(&exec_per_sec.to_le_bytes());
        data.extend_from_slice(&corpus_size.to_le_bytes());
        data.extend_from_slice(&crashes.to_le_bytes());

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::ExecutionCount,
            instance_id: instance_id.to_string(),
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        self.send_entry(entry).await
    }

    /// 快速发送覆盖率命中 - 高频调用优化
    pub async fn send_coverage_hit_fast(
        &self,
        instance_id: &str,
        edge_id: u32,
        hit_count: u32,
        is_new: bool,
    ) -> Result<()> {
        let mut data = Vec::with_capacity(9);
        data.extend_from_slice(&edge_id.to_le_bytes());
        data.extend_from_slice(&hit_count.to_le_bytes());
        data.push(if is_new { 1 } else { 0 });

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CoverageHit,
            instance_id: instance_id.to_string(),
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        self.send_entry(entry).await
    }

    /// 快速发送语料库增长 - 关键事件优化
    pub async fn send_corpus_grow_fast(
        &self,
        instance_id: &str,
        new_inputs: u32,
        total_size: u32,
        avg_length: u32,
    ) -> Result<()> {
        let mut data = Vec::with_capacity(12);
        data.extend_from_slice(&new_inputs.to_le_bytes());
        data.extend_from_slice(&total_size.to_le_bytes());
        data.extend_from_slice(&avg_length.to_le_bytes());

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CorpusGrow,
            instance_id: instance_id.to_string(),
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        self.send_entry(entry).await
    }

    /// 快速发送崩溃发现 - 关键事件优化
    pub async fn send_crash_found_fast(
        &self,
        instance_id: &str,
        crash_type: &str,
        input_hash: u64,
        signal: u32,
    ) -> Result<()> {
        let crash_type_bytes = crash_type.as_bytes();
        let crash_type_len = crash_type_bytes.len() as u32;

        let mut data = Vec::with_capacity(16 + crash_type_bytes.len());
        data.extend_from_slice(&crash_type_len.to_le_bytes());
        data.extend_from_slice(&input_hash.to_le_bytes());
        data.extend_from_slice(&signal.to_le_bytes());
        data.extend_from_slice(crash_type_bytes);

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CrashFound,
            instance_id: instance_id.to_string(),
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            data,
        };

        self.send_entry(entry).await
    }

    /// 强制刷新所有缓冲数据
    pub async fn force_flush(&self) -> Result<()> {
        // 简单实现：通过发送一个特殊的空条目来触发刷新
        // 实际实现可以更复杂，例如添加一个专门的刷新通道
        info!("强制刷新遥测缓冲区");
        Ok(())
    }

    /// 获取缓冲器统计信息
    pub async fn get_stats(&self) -> BufferStats {
        self.stats.read().await.clone()
    }

    /// 获取性能摘要
    pub async fn get_performance_summary(&self) -> String {
        let stats = self.get_stats().await;
        let current_buffered = stats.entries_buffered.saturating_sub(stats.entries_sent);

        format!(
            "高性能遥测缓冲器统计:\n\
             - 缓冲条目: {} (当前积压: {}/{})\n\
             - 已发送条目: {} (批次: {})\n\
             - 平均批量大小: {:.1}\n\
             - 缓冲区利用率: {:.1}%\n\
             - 溢出次数: {}, 发送错误: {}",
            stats.entries_buffered,
            current_buffered,
            self.max_buffer_size,
            stats.entries_sent,
            stats.batches_sent,
            stats.avg_batch_size,
            stats.buffer_utilization * 100.0,
            stats.buffer_overflows,
            stats.send_errors
        )
    }
}

/// 遥测集成助手 - 为fuzzing循环提供便利接口
pub struct TelemetryIntegrationHelper {
    buffer: Arc<HighPerformanceTelemetryBuffer>,
    instance_id: String,
    last_stats_time: Instant,
    stats_interval: Duration,
}

impl TelemetryIntegrationHelper {
    /// 创建遥测集成助手
    pub fn new(
        buffer: Arc<HighPerformanceTelemetryBuffer>,
        instance_id: String,
        stats_interval_secs: u64,
    ) -> Self {
        Self {
            buffer,
            instance_id,
            last_stats_time: Instant::now(),
            stats_interval: Duration::from_secs(stats_interval_secs),
        }
    }

    /// 记录fuzzing迭代统计 - 优化的高频调用
    pub async fn record_fuzzing_iteration(
        &mut self,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    ) {
        // 只在统计间隔后发送，避免过于频繁
        if self.last_stats_time.elapsed() >= self.stats_interval {
            if let Err(e) = self
                .buffer
                .send_execution_stats_fast(
                    &self.instance_id,
                    executions,
                    exec_per_sec,
                    corpus_size,
                    crashes,
                )
                .await
            {
                debug!("发送执行统计失败: {e}");
            }

            self.last_stats_time = Instant::now();
        }
    }

    /// 记录新路径发现 - 关键事件立即发送
    pub async fn record_new_path_found(&self, corpus_size: u32) {
        if let Err(e) = self
            .buffer
            .send_corpus_grow_fast(
                &self.instance_id,
                1, // 新增1个输入
                corpus_size,
                0, // 平均长度待计算
            )
            .await
        {
            debug!("发送语料库增长失败: {e}");
        }
    }

    /// 记录崩溃发现 - 关键事件立即发送
    pub async fn record_crash_found(&self, crash_type: &str, signal: u32) {
        let input_hash = self.generate_input_hash();

        if let Err(e) = self
            .buffer
            .send_crash_found_fast(&self.instance_id, crash_type, input_hash, signal)
            .await
        {
            debug!("发送崩溃发现失败: {e}");
        }
    }

    /// 记录覆盖率命中 - 高频但采样发送
    pub async fn record_coverage_hit(&self, edge_id: u32, hit_count: u32, is_new: bool) {
        // 只有新覆盖或者采样发送以减少频率
        if is_new || hit_count % 100 == 0 {
            if let Err(e) = self
                .buffer
                .send_coverage_hit_fast(&self.instance_id, edge_id, hit_count, is_new)
                .await
            {
                debug!("发送覆盖率命中失败: {e}");
            }
        }
    }

    /// 生成输入哈希
    fn generate_input_hash(&self) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        self.instance_id.hash(&mut hasher);
        SystemTime::now().hash(&mut hasher);
        hasher.finish()
    }
}

/// 遥测条目的高性能写入 - 通过组合而非继承实现
impl HighPerformanceTelemetryBuffer {
    /// 直接写入遥测条目到底层producer - 高性能路径
    async fn write_entry_direct(&self, entry: TelemetryEntry) -> Result<()> {
        // 使用TelemetryProducer现有的公共方法
        // 这些方法已经包含了完整的统计和错误处理
        match entry.data_type {
            TelemetryDataType::ExecutionCount => {
                // 解析执行统计数据
                if entry.data.len() >= 16 {
                    let executions = u32::from_le_bytes([
                        entry.data[0],
                        entry.data[1],
                        entry.data[2],
                        entry.data[3],
                    ]);
                    let exec_per_sec = f32::from_le_bytes([
                        entry.data[4],
                        entry.data[5],
                        entry.data[6],
                        entry.data[7],
                    ]);
                    let corpus_size = u32::from_le_bytes([
                        entry.data[8],
                        entry.data[9],
                        entry.data[10],
                        entry.data[11],
                    ]);
                    let crashes = u32::from_le_bytes([
                        entry.data[12],
                        entry.data[13],
                        entry.data[14],
                        entry.data[15],
                    ]);

                    self.telemetry_producer
                        .send_execution_stats(
                            entry.instance_id,
                            executions,
                            exec_per_sec,
                            corpus_size,
                            crashes,
                        )
                        .await;
                }
                Ok(())
            }
            TelemetryDataType::CoverageHit => {
                // 解析覆盖率命中数据
                if entry.data.len() >= 9 {
                    let edge_id = u32::from_le_bytes([
                        entry.data[0],
                        entry.data[1],
                        entry.data[2],
                        entry.data[3],
                    ]);
                    let hit_count = u32::from_le_bytes([
                        entry.data[4],
                        entry.data[5],
                        entry.data[6],
                        entry.data[7],
                    ]);
                    let is_new = entry.data[8] != 0;

                    self.telemetry_producer
                        .send_coverage_hit(entry.instance_id, edge_id, hit_count, is_new)
                        .await;
                }
                Ok(())
            }
            TelemetryDataType::CrashFound => {
                // 解析崩溃发现数据
                if entry.data.len() >= 16 {
                    let crash_type_len = u32::from_le_bytes([
                        entry.data[0],
                        entry.data[1],
                        entry.data[2],
                        entry.data[3],
                    ]) as usize;
                    let input_hash = u64::from_le_bytes([
                        entry.data[4],
                        entry.data[5],
                        entry.data[6],
                        entry.data[7],
                        entry.data[8],
                        entry.data[9],
                        entry.data[10],
                        entry.data[11],
                    ]);
                    let signal = u32::from_le_bytes([
                        entry.data[12],
                        entry.data[13],
                        entry.data[14],
                        entry.data[15],
                    ]);

                    if entry.data.len() >= 16 + crash_type_len {
                        let crash_type =
                            String::from_utf8_lossy(&entry.data[16..16 + crash_type_len])
                                .to_string();

                        self.telemetry_producer
                            .send_crash_found(entry.instance_id, crash_type, input_hash, signal)
                            .await;
                    }
                }
                Ok(())
            }
            TelemetryDataType::CorpusGrow => {
                // 解析语料库增长数据
                if entry.data.len() >= 12 {
                    let new_inputs = u32::from_le_bytes([
                        entry.data[0],
                        entry.data[1],
                        entry.data[2],
                        entry.data[3],
                    ]);
                    let total_size = u32::from_le_bytes([
                        entry.data[4],
                        entry.data[5],
                        entry.data[6],
                        entry.data[7],
                    ]);
                    let avg_length = u32::from_le_bytes([
                        entry.data[8],
                        entry.data[9],
                        entry.data[10],
                        entry.data[11],
                    ]);

                    self.telemetry_producer
                        .send_corpus_grow(entry.instance_id, new_inputs, total_size, avg_length)
                        .await;
                }
                Ok(())
            }
            TelemetryDataType::MutatorStats => {
                // 解析变异器统计数据
                if entry.data.len() >= 12 {
                    let mutator_id = u32::from_le_bytes([
                        entry.data[0],
                        entry.data[1],
                        entry.data[2],
                        entry.data[3],
                    ]);
                    let usage_count = u32::from_le_bytes([
                        entry.data[4],
                        entry.data[5],
                        entry.data[6],
                        entry.data[7],
                    ]);
                    let success_rate = f32::from_le_bytes([
                        entry.data[8],
                        entry.data[9],
                        entry.data[10],
                        entry.data[11],
                    ]);

                    self.telemetry_producer
                        .send_mutator_stats(
                            entry.instance_id,
                            mutator_id,
                            usage_count,
                            success_rate,
                        )
                        .await;
                }
                Ok(())
            }
            _ => {
                // 其他类型暂时跳过
                debug!("跳过未处理的遥测数据类型: {:?}", entry.data_type);
                Ok(())
            }
        }
    }
}
