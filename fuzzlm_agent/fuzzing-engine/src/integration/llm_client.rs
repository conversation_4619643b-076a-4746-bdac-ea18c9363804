//! LLM集成模块 - 处理与Python端LLM服务的通信

use anyhow::{anyhow, Result};
use log::info;
use tonic::transport::Channel;
use uuid::Uuid;

// 导入生成的gRPC代码
pub mod llm_generation {
    tonic::include_proto!("llm_generation");
}

use llm_generation::{
    llm_code_generation_client::LlmCodeGenerationClient,
    MutatorGenerationRequest,
    ErrorFeedbackRequest,
};

/// LLM集成客户端
pub struct LLMIntegrationClient {
    client: LlmCodeGenerationClient<Channel>,
}

impl LLMIntegrationClient {
    /// 创建新的LLM集成客户端
    pub async fn new(llm_service_addr: &str) -> Result<Self> {
        info!("连接到LLM代码生成服务: {llm_service_addr}");
        
        let channel = Channel::from_shared(llm_service_addr.to_string())?
            .connect()
            .await?;
            
        let client = LlmCodeGenerationClient::new(channel);
        
        Ok(Self { client })
    }
    
    /// 请求生成Mutator代码
    pub async fn generate_mutator_code(
        &mut self,
        target_analysis: &str,
        strategy_config: &str,
        is_repair: bool,
        previous_error: Option<String>,
    ) -> Result<(String, llm_generation::MutatorMetadata)> {
        let request = MutatorGenerationRequest {
            request_id: Uuid::new_v4().to_string(),
            target_analysis: target_analysis.to_string(),
            strategy_config: strategy_config.to_string(),
            is_repair_attempt: is_repair,
            previous_error: previous_error.unwrap_or_default(),
        };
        
        info!("发送Mutator生成请求: {}", request.request_id);
        
        let response = self.client
            .generate_mutator_code(request)
            .await?
            .into_inner();
            
        if response.success {
            info!("✅ 成功接收生成的Mutator代码");
            Ok((
                response.generated_code,
                response.metadata.unwrap_or_default(),
            ))
        } else {
            Err(anyhow!("LLM代码生成失败: {}", response.error_message))
        }
    }
    
    /// 提交错误反馈
    pub async fn submit_error_feedback(
        &mut self,
        error_details: &str,
        validation_results: &str,
    ) -> Result<()> {
        let request = ErrorFeedbackRequest {
            request_id: Uuid::new_v4().to_string(),
            error_details: error_details.to_string(),
            validation_results: validation_results.to_string(),
        };
        
        info!("提交错误反馈: {}", request.request_id);
        
        let response = self.client
            .submit_error_feedback(request)
            .await?
            .into_inner();
            
        if response.acknowledged {
            Ok(())
        } else {
            Err(anyhow!("错误反馈未被确认"))
        }
    }
}