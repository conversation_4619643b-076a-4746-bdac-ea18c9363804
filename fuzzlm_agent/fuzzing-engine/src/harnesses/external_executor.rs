/*!
外部目标程序执行器 - 解决LLVMFuzzerTestOneInput符号冲突问题

用于执行外部编译的目标程序（如libxml2_xml），避免符号冲突。
*/

use std::fs;
use std::io::Write;
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};

use libafl::executors::ExitKind;
use libafl::Error;

/// 外部目标程序执行器
///
/// 通过进程间通信执行外部编译的目标程序，避免链接冲突
pub struct ExternalTargetExecutor {
    /// 目标二进制文件路径
    target_binary: PathBuf,
    /// 超时设置
    timeout: Duration,
    /// 使用标准输入传递数据
    use_stdin: bool,
    /// 临时目录用于文件输入模式
    temp_dir: PathBuf,
    /// 执行统计
    execution_count: Arc<AtomicU64>,
    /// 崩溃计数
    crash_count: Arc<AtomicU64>,
}

impl ExternalTargetExecutor {
    /// 创建新的外部执行器（使用标准输入模式）
    pub fn new(target_binary: PathBuf, timeout_ms: u64) -> Result<Self, Error> {
        let temp_dir = std::env::temp_dir().join(format!("fuzzlm_external_{}", std::process::id()));

        // 确保临时目录存在
        fs::create_dir_all(&temp_dir)
            .map_err(|e| Error::unknown(format!("创建临时目录失败: {e}")))?;

        Ok(Self {
            target_binary,
            timeout: Duration::from_millis(timeout_ms),
            use_stdin: true,
            temp_dir,
            execution_count: Arc::new(AtomicU64::new(0)),
            crash_count: Arc::new(AtomicU64::new(0)),
        })
    }

    /// 创建文件输入模式的执行器
    pub fn with_file_input(target_binary: PathBuf, timeout_ms: u64) -> Result<Self, Error> {
        let mut executor = Self::new(target_binary, timeout_ms)?;
        executor.use_stdin = false;
        Ok(executor)
    }

    /// 执行目标程序
    pub fn execute(&mut self, input: &[u8]) -> Result<ExitKind, Error> {
        let exec_count = self.execution_count.fetch_add(1, Ordering::Relaxed);

        if self.use_stdin {
            self.execute_with_stdin(input, exec_count)
        } else {
            self.execute_with_file(input, exec_count)
        }
    }

    /// 使用标准输入执行目标程序
    fn execute_with_stdin(&mut self, input: &[u8], exec_id: u64) -> Result<ExitKind, Error> {
        let start_time = Instant::now();

        // 启动目标进程
        let mut child = Command::new(&self.target_binary)
            .stdin(Stdio::piped())
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .spawn()
            .map_err(|e| Error::unknown(format!("启动目标程序失败: {e}")))?;

        // 写入输入数据
        if let Some(stdin) = child.stdin.take() {
            let mut stdin = stdin;
            if let Err(e) = stdin.write_all(input) {
                // 如果写入失败，可能是进程已经崩溃
                log::warn!("写入stdin失败 (exec {exec_id}): {e}");
            }
            drop(stdin); // 关闭stdin以通知进程输入结束
        }

        // 等待进程完成或超时
        let result = self.wait_for_completion(&mut child, start_time, exec_id);

        // 确保进程被清理
        let _ = child.kill();
        let _ = child.wait();

        result
    }

    /// 使用文件输入执行目标程序
    fn execute_with_file(&mut self, input: &[u8], exec_id: u64) -> Result<ExitKind, Error> {
        let start_time = Instant::now();

        // 创建临时输入文件
        let input_file = self.temp_dir.join(format!("input_{exec_id}"));
        fs::write(&input_file, input)
            .map_err(|e| Error::unknown(format!("写入临时文件失败: {e}")))?;

        // 启动目标进程
        let mut child = Command::new(&self.target_binary)
            .arg(&input_file)
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .spawn()
            .map_err(|e| Error::unknown(format!("启动目标程序失败: {e}")))?;

        // 等待进程完成
        let result = self.wait_for_completion(&mut child, start_time, exec_id);

        // 清理临时文件
        let _ = fs::remove_file(&input_file);

        // 确保进程被清理
        let _ = child.kill();
        let _ = child.wait();

        result
    }

    /// 等待进程完成并分析结果
    fn wait_for_completion(
        &mut self,
        child: &mut std::process::Child,
        start_time: Instant,
        exec_id: u64,
    ) -> Result<ExitKind, Error> {
        #[allow(unused_assignments)]
        let mut timeout_occurred = false;

        // 简单的超时实现
        loop {
            match child.try_wait() {
                Ok(Some(status)) => {
                    // 进程已完成
                    let exit_kind = self.analyze_exit_status(status, exec_id);

                    if matches!(exit_kind, ExitKind::Crash) {
                        self.crash_count.fetch_add(1, Ordering::Relaxed);
                    }

                    return Ok(exit_kind);
                }
                Ok(None) => {
                    // 进程仍在运行，检查超时
                    if start_time.elapsed() > self.timeout {
                        timeout_occurred = true;
                        break;
                    }

                    // 短暂休眠避免忙等待
                    std::thread::sleep(Duration::from_millis(1));
                }
                Err(e) => {
                    return Err(Error::unknown(format!("等待进程失败: {e}")));
                }
            }
        }

        if timeout_occurred {
            log::debug!("执行超时 (exec {exec_id})");
            Ok(ExitKind::Timeout)
        } else {
            Ok(ExitKind::Ok)
        }
    }

    /// 分析进程退出状态
    fn analyze_exit_status(&self, status: std::process::ExitStatus, exec_id: u64) -> ExitKind {
        if status.success() {
            ExitKind::Ok
        } else if let Some(code) = status.code() {
            // 分析退出码
            match code {
                // 典型的崩溃退出码
                -11 | 139 => {
                    // SIGSEGV
                    log::debug!("检测到段错误 (exec {exec_id}, code: {code})");
                    ExitKind::Crash
                }
                -6 | 134 => {
                    // SIGABRT
                    log::debug!("检测到异常终止 (exec {exec_id}, code: {code})");
                    ExitKind::Crash
                }
                -4 | 132 => {
                    // SIGILL
                    log::debug!("检测到非法指令 (exec {exec_id}, code: {code})");
                    ExitKind::Crash
                }
                -8 | 136 => {
                    // SIGFPE
                    log::debug!("检测到浮点异常 (exec {exec_id}, code: {code})");
                    ExitKind::Crash
                }
                77 => {
                    // 传统的超时退出码
                    ExitKind::Timeout
                }
                _ => {
                    if !(0..=128).contains(&code) {
                        // 负数或大于128通常表示信号终止
                        log::debug!("检测到异常退出 (exec {exec_id}, code: {code})");
                        ExitKind::Crash
                    } else {
                        ExitKind::Ok
                    }
                }
            }
        } else {
            // 没有退出码，可能是被信号终止
            log::debug!("进程被信号终止 (exec {exec_id})");
            ExitKind::Crash
        }
    }

    /// 获取执行统计
    pub fn get_execution_count(&self) -> u64 {
        self.execution_count.load(Ordering::Relaxed)
    }

    /// 获取崩溃计数
    pub fn get_crash_count(&self) -> u64 {
        self.crash_count.load(Ordering::Relaxed)
    }

    /// 获取崩溃率
    pub fn get_crash_rate(&self) -> f64 {
        let total = self.get_execution_count();
        if total == 0 {
            0.0
        } else {
            self.get_crash_count() as f64 / total as f64
        }
    }
}

impl Drop for ExternalTargetExecutor {
    fn drop(&mut self) {
        // 清理临时目录
        if self.temp_dir.exists() {
            let _ = fs::remove_dir_all(&self.temp_dir);
        }
    }
}

/// 为libxml2_xml目标创建专用执行器
pub fn create_libxml2_executor(target_path: PathBuf) -> Result<ExternalTargetExecutor, Error> {
    // libxml2解析通常很快，设置较短超时
    ExternalTargetExecutor::new(target_path, 3000) // 3秒超时
}

