/*!
基础的LibAFL Harness实现
提供一个真实的fuzzing执行器，支持策略配置和遥测数据输出
*/

use std::env;
use std::fs;
use std::io::Write;
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};

use libafl::{
    corpus::{Corpus, InMemoryCorpus, OnDiskCorpus},
    events::SimpleEventManager,
    executors::{inprocess::InProcessExecutor, ExitKind},
    feedbacks::{CrashFeedback, MaxMapFeedback},
    fuzzer::{Fuzzer, StdFuzzer},
    generators::RandPrintablesGenerator,
    inputs::{BytesInput, HasTargetBytes},
    monitors::SimpleMonitor,
    mutators::{havoc_mutations, StdScheduledMutator},
    observers::{HitcountsMapObserver, StdMapObserver, TimeObserver},
    schedulers::QueueScheduler,
    stages::mutational::StdMutationalStage,
    state::{HasCorpus, StdState},
    Error,
};

use libafl_bolts::{rands::StdRand, tuples::tuple_list, AsSlice};
use libafl_targets::{EDGES_MAP, MAX_EDGES_FOUND};

use serde::{Deserialize, Serialize};

/// 策略配置（从文件读取）
#[derive(Debug, Serialize, Deserialize)]
struct StrategyConfig {
    mutator: MutatorConfig,
    scheduler: SchedulerConfig,
    feedbacks: Vec<FeedbackConfig>,
    #[serde(default)]
    stages: Vec<serde_json::Value>,
    #[serde(default)]
    observers: Vec<String>,
    #[serde(default)]
    metadata: std::collections::HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(untagged)]
enum MutatorConfig {
    Simple {
        name: String,
        #[serde(default, rename = "params", alias = "parameters")]
        parameters: std::collections::HashMap<String, serde_json::Value>,
    },
    Scheduled {
        name: String,
        params: ScheduledMutatorParams,
    },
}

#[derive(Debug, Serialize, Deserialize)]
struct ScheduledMutatorParams {
    mutators: Vec<SimpleMutatorConfig>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SimpleMutatorConfig {
    name: String,
    #[serde(default)]
    stages: Option<u32>,
    #[serde(default)]
    stacking: Option<String>,
    #[serde(default)]
    path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SchedulerConfig {
    name: String,
    #[serde(default, rename = "params", alias = "parameters")]
    parameters: std::collections::HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
struct FeedbackConfig {
    name: String,
    #[serde(default, rename = "params", alias = "parameters")]
    parameters: std::collections::HashMap<String, serde_json::Value>,
}

/// 统计信息
#[derive(Clone)]
struct FuzzingStats {
    start_time: Instant,
    total_executions: Arc<AtomicU64>,
    corpus_size: Arc<AtomicU64>,
    crashes_found: Arc<AtomicU64>,
    covered_edges: Arc<AtomicU64>,
}

impl FuzzingStats {
    fn new() -> Self {
        Self {
            start_time: Instant::now(),
            total_executions: Arc::new(AtomicU64::new(0)),
            corpus_size: Arc::new(AtomicU64::new(0)),
            crashes_found: Arc::new(AtomicU64::new(0)),
            covered_edges: Arc::new(AtomicU64::new(0)),
        }
    }

    fn write_stats(&self, output_dir: &Path) -> Result<(), Error> {
        let runtime = self.start_time.elapsed();
        let exec_per_sec =
            self.total_executions.load(Ordering::Relaxed) as f64 / runtime.as_secs_f64();

        let stats = format!(
            r#"{{
    "total_executions": {},
    "corpus_size": {},
    "crashes": {},
    "covered_edges": {},
    "runtime_seconds": {},
    "exec_per_sec": {:.2}
}}"#,
            self.total_executions.load(Ordering::Relaxed),
            self.corpus_size.load(Ordering::Relaxed),
            self.crashes_found.load(Ordering::Relaxed),
            self.covered_edges.load(Ordering::Relaxed),
            runtime.as_secs(),
            exec_per_sec
        );

        fs::write(output_dir.join("fuzzer_stats"), stats)?;
        Ok(())
    }
}

/// Harness函数类型
type HarnessFn = fn(&[u8]) -> ExitKind;

/// 主fuzzing函数
pub fn run_fuzzing(
    target_path: PathBuf,
    work_dir: PathBuf,
    output_dir: PathBuf,
    strategy_file: PathBuf,
    instance_id: String,
    harness_fn: HarnessFn,
) -> Result<(), Error> {
    println!("LibAFL Harness 启动");
    println!("实例ID: {instance_id}");
    println!("目标: {target_path:?}");
    println!("工作目录: {work_dir:?}");
    println!("输出目录: {output_dir:?}");

    // 读取策略配置
    let strategy: StrategyConfig = if strategy_file.exists() {
        let content = fs::read_to_string(&strategy_file)?;
        serde_json::from_str(&content)?
    } else {
        // 默认策略
        StrategyConfig {
            mutator: MutatorConfig::Simple {
                name: "havoc".to_string(),
                parameters: Default::default(),
            },
            scheduler: SchedulerConfig {
                name: "queue".to_string(),
                parameters: Default::default(),
            },
            feedbacks: vec![FeedbackConfig {
                name: "MaxMapFeedback".to_string(),
                parameters: Default::default(),
            }],
            stages: Vec::new(),
            observers: vec!["edges".to_string()],
            metadata: Default::default(),
        }
    };

    println!("策略: {strategy:?}");

    // 初始化统计信息
    let stats = FuzzingStats::new();
    let stats_clone = stats.total_executions.clone();

    // 创建观察器
    let edges_observer = unsafe {
        HitcountsMapObserver::new(StdMapObserver::from_mut_ptr(
            "edges",
            std::ptr::addr_of_mut!(EDGES_MAP).cast::<u8>(),
            MAX_EDGES_FOUND,
        ))
    };

    let time_observer = TimeObserver::new("time");

    // 创建反馈
    let mut feedback = MaxMapFeedback::new(&edges_observer);
    let mut objective = CrashFeedback::new();

    // 创建状态
    let mut state = StdState::new(
        StdRand::new(),
        InMemoryCorpus::new(),
        OnDiskCorpus::new(output_dir.join("crashes"))?,
        &mut feedback,
        &mut objective,
    )?;

    // 创建监视器
    let monitor = SimpleMonitor::new(|s| {
        println!("{s}");
    });

    // 创建事件管理器
    let mut mgr = SimpleEventManager::new(monitor);

    // 创建调度器
    let scheduler = QueueScheduler::new();

    // 创建fuzzer
    let mut fuzzer = StdFuzzer::new(scheduler, feedback, objective);

    // 创建执行器
    let mut harness = |input: &BytesInput| {
        let target = input.target_bytes();
        let buf = target.as_slice();

        // 更新执行计数
        stats_clone.fetch_add(1, Ordering::Relaxed);

        harness_fn(buf)
    };

    let in_proc_executor = InProcessExecutor::new(
        &mut harness,
        tuple_list!(edges_observer, time_observer),
        &mut fuzzer,
        &mut state,
        &mut mgr,
    )?;

    // 使用进程内执行器（不使用TimeoutExecutor）
    let mut executor = in_proc_executor;

    // 创建生成器（用于初始种子）
    let mut generator = RandPrintablesGenerator::new(32).unwrap();

    // 生成初始输入
    match state.generate_initial_inputs(&mut fuzzer, &mut executor, &mut generator, &mut mgr, 8) {
        Ok(_) => {
            println!("生成了 {} 个初始输入", state.corpus().count());
        }
        Err(e) => {
            eprintln!("生成初始输入失败: {e:?}");
            // 继续执行，即使没有初始输入
            println!("警告: 没有初始输入，fuzzer将从空corpus开始");
        }
    }

    // 创建变异器
    let mutator = StdScheduledMutator::new(havoc_mutations());

    // 创建阶段
    let mut stages = tuple_list!(StdMutationalStage::new(mutator));

    // 启动统计输出任务
    let stats_total_exec = stats.total_executions.clone();
    let stats_corpus_size = stats.corpus_size.clone();
    let stats_crashes = stats.crashes_found.clone();
    let stats_edges = stats.covered_edges.clone();
    let stats_start_time = stats.start_time;
    let output_dir_clone = output_dir.clone();

    std::thread::spawn(move || {
        loop {
            std::thread::sleep(Duration::from_secs(1));

            // 统计覆盖的边
            let mut covered = 0u64;
            unsafe {
                let edges_ptr = std::ptr::addr_of!(EDGES_MAP);
                let edges_slice = std::slice::from_raw_parts(edges_ptr.cast::<u8>(), MAX_EDGES_FOUND);
                for &edge in edges_slice.iter() {
                    if edge > 0 {
                        covered += 1;
                    }
                }
            }
            stats_edges.store(covered, Ordering::Relaxed);

            // 写入统计文件
            let runtime = stats_start_time.elapsed();
            let exec_per_sec =
                stats_total_exec.load(Ordering::Relaxed) as f64 / runtime.as_secs_f64();

            let stats_json = format!(
                r#"{{
    "total_executions": {},
    "corpus_size": {},
    "crashes": {},
    "covered_edges": {},
    "runtime_seconds": {},
    "exec_per_sec": {:.2}
}}"#,
                stats_total_exec.load(Ordering::Relaxed),
                stats_corpus_size.load(Ordering::Relaxed),
                stats_crashes.load(Ordering::Relaxed),
                stats_edges.load(Ordering::Relaxed),
                runtime.as_secs(),
                exec_per_sec
            );

            let _ = fs::write(output_dir_clone.join("fuzzer_stats"), stats_json);

            // 打印到stdout（用于日志解析）
            println!(
                "corpus: {}, crashes: {}, exec/s: {:.0}, edges: {}",
                stats_corpus_size.load(Ordering::Relaxed),
                stats_crashes.load(Ordering::Relaxed),
                exec_per_sec,
                stats_edges.load(Ordering::Relaxed)
            );
        }
    });

    // 主fuzzing循环
    println!("开始fuzzing...");
    println!("Corpus大小: {}", state.corpus().count());
    let corpus_size_updater = stats.corpus_size.clone();
    let _crashes_updater = stats.crashes_found.clone();

    loop {
        fuzzer.fuzz_one(&mut stages, &mut executor, &mut state, &mut mgr)?;

        // 更新统计
        corpus_size_updater.store(state.corpus().count() as u64, Ordering::Relaxed);

        // 检查是否应该停止
        if let Ok(stop_flag) = env::var("FUZZER_STOP") {
            if stop_flag == "1" {
                println!("收到停止信号");
                break;
            }
        }

        // 定期保存状态
        if stats.total_executions.load(Ordering::Relaxed) % 10000 == 0 {
            // 可以在这里保存检查点
        }
    }

    println!("Fuzzing结束");
    stats.write_stats(&output_dir)?;

    Ok(())
}

/// 默认的测试harness - 用于演示崩溃检测功能
/// 这是一个真实的测试用例，不是模拟数据
fn default_harness(input: &[u8]) -> ExitKind {
    // 崩溃检测逻辑：检测特定模式以触发崩溃
    // 这是fuzzing的正常行为，用于测试崩溃检测能力
    if !input.is_empty() && input[0] == b'A' && input.len() > 10
        && input[1] == b'B' && input[2] == b'C' {
            // 触发真实崩溃以测试fuzzer的崩溃检测功能
            panic!("Found ABC pattern!");
        }

    ExitKind::Ok
}

/// 外部harness执行器 - 用于避免LLVMFuzzerTestOneInput符号冲突
pub fn run_external_target_fuzzing(
    target_binary: PathBuf,
    work_dir: PathBuf,
    output_dir: PathBuf,
    strategy_file: PathBuf,
    instance_id: String,
) -> Result<(), Error> {
    // 直接嵌入外部执行器定义以避免导入问题
    use std::process::{Command, Stdio};
    use std::time::{Duration, Instant};

    /// 外部目标执行器 - 用于执行外部编译的目标程序
    pub struct ExternalTargetExecutor {
        pub target_path: PathBuf,
        pub timeout_ms: u64,
        pub stats: Arc<ExternalExecutorStats>,
    }

    /// 外部执行器统计信息
    pub struct ExternalExecutorStats {
        pub executions: AtomicU64,
        pub timeouts: AtomicU64,
        pub crashes: AtomicU64,
        pub normal_exits: AtomicU64,
    }

    impl ExternalTargetExecutor {
        pub fn new(target_path: PathBuf, timeout_ms: u64) -> Result<Self, Error> {
            Ok(Self {
                target_path,
                timeout_ms,
                stats: Arc::new(ExternalExecutorStats {
                    executions: AtomicU64::new(0),
                    timeouts: AtomicU64::new(0),
                    crashes: AtomicU64::new(0),
                    normal_exits: AtomicU64::new(0),
                }),
            })
        }

        pub fn execute(&self, input: &[u8]) -> Result<ExitKind, Error> {
            self.stats.executions.fetch_add(1, Ordering::Relaxed);

            let start_time = Instant::now();
            let timeout = Duration::from_millis(self.timeout_ms);

            let mut child = Command::new(&self.target_path)
                .stdin(Stdio::piped())
                .stdout(Stdio::null())
                .stderr(Stdio::null())
                .spawn()
                .map_err(|e| Error::unknown(format!("启动外部进程失败: {e}")))?;

            // 写入输入数据
            if let Some(stdin) = child.stdin.as_mut() {
                let _ = stdin.write_all(input);
            }

            // 等待执行完成或超时
            loop {
                match child.try_wait() {
                    Ok(Some(status)) => {
                        // 进程已完成
                        if status.success() {
                            self.stats.normal_exits.fetch_add(1, Ordering::Relaxed);
                            return Ok(ExitKind::Ok);
                        } else {
                            self.stats.crashes.fetch_add(1, Ordering::Relaxed);
                            return Ok(ExitKind::Crash);
                        }
                    }
                    Ok(None) => {
                        // 进程仍在运行，检查超时
                        if start_time.elapsed() > timeout {
                            let _ = child.kill();
                            let _ = child.wait();
                            self.stats.timeouts.fetch_add(1, Ordering::Relaxed);
                            return Ok(ExitKind::Timeout);
                        }
                        std::thread::sleep(Duration::from_millis(10));
                    }
                    Err(_) => {
                        // 错误情况
                        self.stats.crashes.fetch_add(1, Ordering::Relaxed);
                        return Ok(ExitKind::Crash);
                    }
                }
            }
        }
    }

    println!("外部目标LibAFL Harness启动");
    println!("实例ID: {instance_id}");
    println!("目标二进制: {target_binary:?}");
    println!("工作目录: {work_dir:?}");
    println!("输出目录: {output_dir:?}");

    // 创建外部执行器
    let external_executor = ExternalTargetExecutor::new(target_binary.clone(), 5000)?;

    // 读取策略配置（复用现有逻辑）
    let strategy: StrategyConfig = if strategy_file.exists() {
        let content = fs::read_to_string(&strategy_file)?;
        serde_json::from_str(&content)?
    } else {
        // 为libxml2优化的默认策略
        StrategyConfig {
            mutator: MutatorConfig::Simple {
                name: "havoc".to_string(),
                parameters: Default::default(),
            },
            scheduler: SchedulerConfig {
                name: "queue".to_string(),
                parameters: Default::default(),
            },
            feedbacks: vec![FeedbackConfig {
                name: "MaxMapFeedback".to_string(),
                parameters: Default::default(),
            }],
            stages: Vec::new(),
            observers: vec!["edges".to_string()],
            metadata: Default::default(),
        }
    };

    println!("外部目标策略: {strategy:?}");

    // 初始化统计信息
    let stats = FuzzingStats::new();
    let stats_clone = stats.total_executions.clone();

    // 创建观察器（不需要覆盖率map，因为是外部执行）
    let edges_observer = unsafe {
        HitcountsMapObserver::new(StdMapObserver::from_mut_ptr(
            "edges",
            std::ptr::addr_of_mut!(EDGES_MAP).cast::<u8>(),
            MAX_EDGES_FOUND,
        ))
    };

    let time_observer = TimeObserver::new("time");

    // 创建反馈（简化版，因为外部执行）
    let mut feedback = MaxMapFeedback::new(&edges_observer);
    let mut objective = CrashFeedback::new();

    // 创建状态
    let mut state = StdState::new(
        StdRand::new(),
        InMemoryCorpus::new(),
        OnDiskCorpus::new(output_dir.join("crashes"))?,
        &mut feedback,
        &mut objective,
    )?;

    // 创建监视器
    let monitor = SimpleMonitor::new(|s| {
        println!("{s}");
    });

    // 创建事件管理器
    let mut mgr = SimpleEventManager::new(monitor);

    // 创建调度器
    let scheduler = QueueScheduler::new();

    // 创建fuzzer
    let mut fuzzer = StdFuzzer::new(scheduler, feedback, objective);

    // 创建包装的harness函数
    let mut harness = |input: &BytesInput| {
        let target = input.target_bytes();
        let buf = target.as_slice();

        // 更新执行计数
        stats_clone.fetch_add(1, Ordering::Relaxed);

        // 调用外部执行器
        match external_executor.execute(buf) {
            Ok(exit_kind) => exit_kind,
            Err(e) => {
                eprintln!("外部执行失败: {e}");
                ExitKind::Ok
            }
        }
    };

    let in_proc_executor = InProcessExecutor::new(
        &mut harness,
        tuple_list!(edges_observer, time_observer),
        &mut fuzzer,
        &mut state,
        &mut mgr,
    )?;

    let mut executor = in_proc_executor;

    // 生成初始输入（针对XML优化）
    let xml_seeds = vec![
        b"<test></test>".to_vec(),
        b"<xml><item>data</item></xml>".to_vec(),
        b"<?xml version=\"1.0\"?><root></root>".to_vec(),
        b"<doc><elem attr=\"value\"/></doc>".to_vec(),
    ];

    for seed in xml_seeds {
        let seed_input = BytesInput::new(seed);
        match state.corpus_mut().add(seed_input.into()) {
            Ok(_) => {}
            Err(e) => eprintln!("添加种子失败: {e}"),
        }
    }

    println!("初始语料库大小: {}", state.corpus().count());

    // 创建变异器
    let mutator = StdScheduledMutator::new(havoc_mutations());

    // 创建阶段
    let mut stages = tuple_list!(StdMutationalStage::new(mutator));

    // 启动统计输出任务（复用现有逻辑）
    let stats_total_exec = stats.total_executions.clone();
    let stats_corpus_size = stats.corpus_size.clone();
    let stats_crashes = stats.crashes_found.clone();
    let stats_edges = stats.covered_edges.clone();
    let stats_start_time = stats.start_time;
    let output_dir_clone = output_dir.clone();

    std::thread::spawn(move || {
        loop {
            std::thread::sleep(Duration::from_secs(5)); // 外部执行频率较低

            // 写入统计文件
            let runtime = stats_start_time.elapsed();
            let exec_per_sec =
                stats_total_exec.load(Ordering::Relaxed) as f64 / runtime.as_secs_f64();

            let stats_json = format!(
                r#"{{
    "total_executions": {},
    "corpus_size": {},
    "crashes": {},
    "covered_edges": {},
    "runtime_seconds": {},
    "exec_per_sec": {:.2},
    "external_mode": true
}}"#,
                stats_total_exec.load(Ordering::Relaxed),
                stats_corpus_size.load(Ordering::Relaxed),
                stats_crashes.load(Ordering::Relaxed),
                stats_edges.load(Ordering::Relaxed),
                runtime.as_secs(),
                exec_per_sec
            );

            let _ = fs::write(output_dir_clone.join("fuzzer_stats"), stats_json);

            // 打印到stdout
            println!(
                "外部模式 - corpus: {}, crashes: {}, exec/s: {:.0}, edges: {}",
                stats_corpus_size.load(Ordering::Relaxed),
                stats_crashes.load(Ordering::Relaxed),
                exec_per_sec,
                stats_edges.load(Ordering::Relaxed)
            );
        }
    });

    // 主fuzzing循环
    println!("开始外部目标fuzzing...");
    let corpus_size_updater = stats.corpus_size.clone();
    let _crashes_updater = stats.crashes_found.clone();

    loop {
        match fuzzer.fuzz_one(&mut stages, &mut executor, &mut state, &mut mgr) {
            Ok(_) => {
                // 更新统计
                corpus_size_updater.store(state.corpus().count() as u64, Ordering::Relaxed);
            }
            Err(e) => {
                eprintln!("Fuzzing迭代失败: {e}");
                // 对于外部执行，错误更常见，不立即退出
            }
        }

        // 检查停止条件
        if let Ok(stop_flag) = env::var("FUZZER_STOP") {
            if stop_flag == "1" {
                println!("收到停止信号");
                break;
            }
        }

        // 定期输出外部执行器统计（在fuzzing循环外收集）
        if stats.total_executions.load(Ordering::Relaxed) % 100 == 0 {
            // 由于borrowing限制，我们只输出基本统计信息
            println!(
                "外部执行器统计 - 总执行: {}",
                stats.total_executions.load(Ordering::Relaxed)
            );
        }
    }

    println!("外部目标Fuzzing结束");
    stats.write_stats(&output_dir)?;

    Ok(())
}

/// 检测是否应该使用外部harness模式
pub fn should_use_external_harness(target_path: &Path) -> bool {
    // 检查目标是否为已知的外部目标
    let target_str = target_path.to_string_lossy().to_lowercase();

    // 已知需要外部harness的目标
    let external_targets = [
        "libxml2",
        "libxml2_xml",
        "xml",
        "target.cc",
        "target.c",
        "parser",
        "fuzzbench",
        "benchmark",
        "external",
    ];

    let is_external = external_targets
        .iter()
        .any(|&pattern| target_str.contains(pattern));

    println!(
        "🔍 外部harness检测: 目标={target_str}, 是否外部={is_external}"
    );

    is_external
}

/// 主入口点 - 自动选择内部或外部harness模式
pub fn run_adaptive_fuzzing(
    target_path: PathBuf,
    work_dir: PathBuf,
    output_dir: PathBuf,
    strategy_file: PathBuf,
    instance_id: String,
) -> Result<(), Error> {
    if should_use_external_harness(&target_path) {
        println!("检测到外部目标，使用外部harness模式");
        run_external_target_fuzzing(
            target_path,
            work_dir,
            output_dir,
            strategy_file,
            instance_id,
        )
    } else {
        println!("使用内部harness模式");
        run_fuzzing(
            target_path,
            work_dir,
            output_dir,
            strategy_file,
            instance_id,
            default_harness,
        )
    }
}

/// 主函数 - LibAFL Harness 二进制入口点
#[allow(dead_code)]
fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();
    log::info!("LibAFL Harness 启动");
    
    // 这是一个库二进制文件，主要功能通过其他方式调用
    // 在实际使用中会通过 instance manager 启动
    Ok(())
}
