/*!
统一目标编译器模块
====================

用于解决AddressSanitizer运行时错误的架构重构方案。

问题根因：
- Python Phase2处理器使用 `-fsanitize=address` 编译目标
- Rust引擎缺少相应的ASan配置
- 动态加载ASan库到非ASan进程导致运行时错误

解决方案：
- 统一编译流程在Rust端
- 智能编译模式选择
- 确保编译标志完全一致
*/

use anyhow::{anyhow, Result};
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use std::process::{Command, Stdio};

/// 编译配置选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompilationConfig {
    /// 编译模式：debug 或 release
    pub mode: CompilationMode,
    /// 是否启用AddressSanitizer
    pub enable_asan: bool,
    /// 是否启用LibAFL覆盖率instrumentation
    pub enable_libafl_coverage: bool,
    /// 优化级别 (0-3)
    pub optimization_level: u8,
    /// 额外的编译标志
    pub extra_flags: Vec<String>,
    /// 目标架构
    pub target_arch: Option<String>,
}

impl Default for CompilationConfig {
    fn default() -> Self {
        Self {
            mode: CompilationMode::Release,
            enable_asan: false,           // 🔧 默认禁用ASan以解决兼容性问题
            enable_libafl_coverage: true, // ✅ 启用LibAFL覆盖率
            optimization_level: 2,
            extra_flags: Vec::new(),
            target_arch: None,
        }
    }
}

/// 编译模式
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum CompilationMode {
    Debug,
    Release,
    /// 智能模式：根据目标特征自动选择
    Auto,
}

/// 编译结果
#[derive(Debug, Clone)]
pub struct CompilationResult {
    /// 编译是否成功
    pub success: bool,
    /// 输出文件路径
    pub output_path: Option<PathBuf>,
    /// 编译用时（秒）
    pub compilation_time: f64,
    /// 编译器输出
    pub compiler_output: String,
    /// 编译器错误输出
    pub compiler_errors: String,
    /// 使用的编译配置
    pub config: CompilationConfig,
}

/// 统一目标编译器
pub struct UnifiedTargetCompiler {
    /// 默认编译配置
    default_config: CompilationConfig,
    /// 工作目录（用于临时文件）
    #[allow(dead_code)]
    work_dir: PathBuf,
    /// 输出目录
    output_dir: PathBuf,
}

impl UnifiedTargetCompiler {
    /// 创建新的统一编译器
    pub fn new(work_dir: PathBuf, output_dir: PathBuf) -> Result<Self> {
        // 确保目录存在
        fs::create_dir_all(&work_dir)?;
        fs::create_dir_all(&output_dir)?;

        Ok(Self {
            default_config: CompilationConfig::default(),
            work_dir,
            output_dir,
        })
    }

    /// 设置默认编译配置
    pub fn with_default_config(mut self, config: CompilationConfig) -> Self {
        self.default_config = config;
        self
    }

    /// 编译C/C++目标为共享库
    pub fn compile_target(
        &self,
        target_path: &Path,
        config: Option<CompilationConfig>,
    ) -> Result<CompilationResult> {
        let start_time = std::time::Instant::now();
        let config = config.unwrap_or_else(|| self.default_config.clone());

        info!("🔨 统一编译器开始编译目标: {target_path:?}");
        debug!("编译配置: {config:?}");

        // 验证源文件存在
        if !target_path.exists() {
            return Err(anyhow!("目标文件不存在: {:?}", target_path));
        }

        // 检测源文件类型
        let source_type = self.detect_source_type(target_path)?;
        debug!("检测到源文件类型: {source_type:?}");

        // 生成输出文件名
        let output_file = self.generate_output_filename(target_path, &source_type);
        let output_path = self.output_dir.join(&output_file);

        info!("📁 输出路径: {output_path:?}");

        // 构建编译命令
        let compile_result =
            self.build_and_execute_command(target_path, &output_path, &config, &source_type);

        let compilation_time = start_time.elapsed().as_secs_f64();

        match compile_result {
            Ok((output, errors)) => {
                if output_path.exists() {
                    info!(
                        "✅ 编译成功: {output_path:?} (用时: {compilation_time:.2}s)"
                    );
                    Ok(CompilationResult {
                        success: true,
                        output_path: Some(output_path.canonicalize()?),
                        compilation_time,
                        compiler_output: output,
                        compiler_errors: errors,
                        config,
                    })
                } else {
                    error!("❌ 编译完成但输出文件不存在: {output_path:?}");
                    Ok(CompilationResult {
                        success: false,
                        output_path: None,
                        compilation_time,
                        compiler_output: output,
                        compiler_errors: errors,
                        config,
                    })
                }
            }
            Err(e) => {
                error!("❌ 编译失败: {e}");
                Ok(CompilationResult {
                    success: false,
                    output_path: None,
                    compilation_time,
                    compiler_output: String::new(),
                    compiler_errors: format!("编译异常: {e}"),
                    config,
                })
            }
        }
    }

    /// 检测源文件类型
    fn detect_source_type(&self, path: &Path) -> Result<SourceType> {
        match path.extension().and_then(|s| s.to_str()) {
            Some("c") => Ok(SourceType::C),
            Some("cpp") | Some("cc") | Some("cxx") => Ok(SourceType::Cpp),
            Some(ext) => Err(anyhow!("不支持的源文件类型: {}", ext)),
            None => Err(anyhow!("无法确定源文件类型: {:?}", path)),
        }
    }

    /// 生成输出文件名
    fn generate_output_filename(&self, source_path: &Path, _source_type: &SourceType) -> String {
        let stem = source_path
            .file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("target");

        format!("lib{stem}.so")
    }

    /// 构建并执行编译命令
    fn build_and_execute_command(
        &self,
        source_path: &Path,
        output_path: &Path,
        config: &CompilationConfig,
        source_type: &SourceType,
    ) -> Result<(String, String)> {
        // 选择编译器
        let compiler = match source_type {
            SourceType::C => "clang",     // 🎯 强制使用clang以支持sanitizers
            SourceType::Cpp => "clang++", // 🎯 强制使用clang++以支持sanitizers
        };

        let mut cmd = Command::new(compiler);

        // 基础参数
        cmd.arg("-shared")
            .arg("-fPIC")
            .arg("-o")
            .arg(output_path)
            .arg(source_path);

        // 🔧 关键修复：根据配置添加一致的编译标志
        self.add_compilation_flags(&mut cmd, config)?;

        // 执行编译
        info!("🚀 执行编译命令: {cmd:?}");

        let output = cmd.stdout(Stdio::piped()).stderr(Stdio::piped()).output()?;

        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        let stderr = String::from_utf8_lossy(&output.stderr).to_string();

        if !output.status.success() {
            warn!("⚠️ 编译器返回非零状态码");
            debug!("编译器stdout: {stdout}");
            debug!("编译器stderr: {stderr}");
        }

        Ok((stdout, stderr))
    }

    /// 🔧 核心修复：添加统一编译标志
    fn add_compilation_flags(&self, cmd: &mut Command, config: &CompilationConfig) -> Result<()> {
        // 优化级别
        cmd.arg(format!("-O{}", config.optimization_level));

        // 调试信息
        match config.mode {
            CompilationMode::Debug => {
                cmd.arg("-g").arg("-DDEBUG");
            }
            CompilationMode::Release => {
                cmd.arg("-g").arg("-DNDEBUG");
            }
            CompilationMode::Auto => {
                cmd.arg("-g"); // 始终保留调试信息
            }
        }

        // 🎯 关键修复：AddressSanitizer配置
        if config.enable_asan {
            info!("🛡️ 启用AddressSanitizer");
            cmd.arg("-fsanitize=address")
                .arg("-fsanitize-recover=address")
                .arg("-fno-omit-frame-pointer");
        }

        // 🎯 关键修复：LibAFL覆盖率配置
        if config.enable_libafl_coverage {
            info!("📊 启用LibAFL覆盖率instrumentation");
            // 🔧 修复：使用更兼容的覆盖率选项，避免符号未定义错误
            // 注意：LibAFL在运行时提供自己的覆盖率机制，不需要编译时的sanitizer coverage
            cmd.arg("-fPIC").arg("-fvisibility=default");

            // 仅在需要时添加基础的coverage支持
            if std::env::var("FUZZLM_USE_SANCOV").unwrap_or_default() == "1" {
                cmd.arg("-fsanitize-coverage=trace-pc-guard,trace-cmp");
            }
        }

        // 兼容性标志
        cmd.arg("-Wno-sign-compare").arg("-Wno-unused-parameter");

        // 额外标志
        for flag in &config.extra_flags {
            cmd.arg(flag);
        }

        Ok(())
    }

    /// 🔧 智能配置推荐：根据环境自动选择最佳配置
    pub fn recommend_config(&self, target_path: &Path) -> Result<CompilationConfig> {
        let mut config = CompilationConfig::default();

        // 🎯 根据环境变量决定是否启用ASan
        if std::env::var("FUZZLM_ENABLE_ASAN").unwrap_or_default() == "1" {
            info!("🛡️ 环境变量启用AddressSanitizer");
            config.enable_asan = true;
            config.optimization_level = 1; // ASan建议使用-O1
        }

        // 根据目标文件特征调整配置
        if let Ok(content) = fs::read_to_string(target_path) {
            // 如果包含main函数，使用调试模式
            if content.contains("int main(") || content.contains("void main(") {
                config.mode = CompilationMode::Debug;
                info!("📋 检测到main函数，使用调试模式");
            }

            // 如果包含特定的危险函数，启用额外检查
            let dangerous_funcs = vec!["strcpy", "strcat", "sprintf", "memcpy"];
            for func in dangerous_funcs {
                if content.contains(func) {
                    config.extra_flags.push("-D_FORTIFY_SOURCE=2".to_string());
                    info!("🛡️ 检测到危险函数{func}，启用额外保护");
                    break;
                }
            }
        }

        Ok(config)
    }

    /// 批量编译多个目标
    pub fn compile_multiple_targets(
        &self,
        targets: &[PathBuf],
        config: Option<CompilationConfig>,
    ) -> Result<Vec<CompilationResult>> {
        let mut results = Vec::new();

        for target in targets {
            let result = self.compile_target(target, config.clone())?;
            let success = result.success; // 提前获取success字段
            results.push(result);

            if !success {
                warn!("⚠️ 目标编译失败: {target:?}");
            }
        }

        Ok(results)
    }
}

/// 源文件类型
#[derive(Debug, Clone)]
enum SourceType {
    C,
    Cpp,
}

/// 🔧 创建兼容性配置的便捷函数
impl CompilationConfig {
    /// 创建调试配置（启用ASan）
    pub fn debug_with_asan() -> Self {
        Self {
            mode: CompilationMode::Debug,
            enable_asan: true,
            enable_libafl_coverage: true,
            optimization_level: 1,
            extra_flags: vec![
                "-D_FORTIFY_SOURCE=2".to_string(),
                "-fstack-protector-strong".to_string(),
            ],
            target_arch: None,
        }
    }

    /// 创建生产配置（禁用ASan，高性能）
    pub fn release_high_performance() -> Self {
        Self {
            mode: CompilationMode::Release,
            enable_asan: false,           // 🎯 生产环境禁用ASan
            enable_libafl_coverage: true, // ✅ 保持LibAFL覆盖率
            optimization_level: 2,
            extra_flags: vec!["-DNDEBUG".to_string(), "-ffast-math".to_string()],
            target_arch: None,
        }
    }

    /// 创建兼容配置（解决当前ASan问题）
    pub fn compatibility_fix() -> Self {
        Self {
            mode: CompilationMode::Release,
            enable_asan: false,           // 🔧 临时禁用ASan解决运行时错误
            enable_libafl_coverage: true, // ✅ 保持LibAFL兼容性
            optimization_level: 2,
            extra_flags: vec!["-DLIBAFL_COMPATIBLE".to_string()],
            target_arch: None,
        }
    }

    /// 🔧 创建LibAFL优化配置（无sanitizer coverage）
    pub fn libafl_optimized() -> Self {
        Self {
            mode: CompilationMode::Release,
            enable_asan: false,           // 禁用ASan避免符号冲突
            enable_libafl_coverage: true, // 启用LibAFL支持但不使用sanitizer coverage
            optimization_level: 2,
            extra_flags: vec![
                "-DLIBAFL_COMPATIBLE".to_string(),
                "-fPIC".to_string(),   // 位置无关代码
                "-shared".to_string(), // 共享库
            ],
            target_arch: None,
        }
    }
}

