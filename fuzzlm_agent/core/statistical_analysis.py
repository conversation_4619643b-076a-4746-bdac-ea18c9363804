"""Statistical Analysis Module for FuzzLM-Agent.

处理来自共享内存数据平面的遥测数据流的统计分析

Phase 3.1: 高性能遥测数据分析
"""

from __future__ import annotations

import asyncio
import logging
import statistics
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from typing import Any

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能度量数据类."""

    # 基础指标
    total_executions: int = 0
    execution_rate: float = 0.0  # 执行速率 (exec/s)

    # 实例标识
    instance_id: str | None = None  # 用于区分champion/shadow

    # 覆盖率指标
    coverage_hits: int = 0
    unique_edges: int = 0
    coverage_percentage: float = 0.0
    path_discovery_rate: float = 0.0  # 新路径发现率 (paths/min)
    new_paths: int = 0  # 新发现路径数
    edge_coverage: float = 0.0  # 边覆盖率百分比 (兼容性)

    # 崩溃和漏洞
    crashes_found: int = 0
    unique_crashes: int = 0
    crash_rate: float = 0.0  # 崩溃率 (crashes/1M execs)

    # 语料库指标
    corpus_size: int = 0
    corpus_growth_rate: float = 0.0  # 语料库增长率 (inputs/min)
    avg_input_length: int = 0

    # 效率指标
    efficiency_score: float = 0.0  # 综合效率评分 (0-1)
    mutation_efficiency: float = 0.0  # 变异效率 (interesting/total)
    energy_efficiency: float = 0.0  # 能量分配效率

    # 资源使用
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0

    # 时间窗口
    window_start: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    window_end: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def calculate_efficiency(self) -> float:
        """计算综合效率评分"""
        # 基于多个因素计算效率评分
        if self.total_executions == 0:
            return 0.0

        # 新路径发现效率 (权重 0.4)
        path_efficiency = min(
            self.path_discovery_rate / 10.0,
            1.0,
        )  # 假设10 paths/min为满分

        # 崩溃发现效率 (权重 0.3)
        crash_efficiency = min(self.crash_rate / 100.0, 1.0)  # 假设100 crashes/1M为满分

        # 执行速度效率 (权重 0.2)
        exec_efficiency = min(
            self.execution_rate / 100000.0,
            1.0,
        )  # 假设100k exec/s为满分

        # 语料库增长效率 (权重 0.1)
        corpus_efficiency = min(
            self.corpus_growth_rate / 5.0,
            1.0,
        )  # 假设5 inputs/min为满分

        self.efficiency_score = (
            path_efficiency * 0.4
            + crash_efficiency * 0.3
            + exec_efficiency * 0.2
            + corpus_efficiency * 0.1
        )

        return self.efficiency_score


@dataclass
class StatisticalSummary:
    """统计摘要数据"""

    mean: float = 0.0
    median: float = 0.0
    std_dev: float = 0.0
    min_val: float = 0.0
    max_val: float = 0.0
    percentile_25: float = 0.0
    percentile_75: float = 0.0
    percentile_95: float = 0.0
    trend: str = "stable"  # increasing, decreasing, stable, volatile

    @classmethod
    def from_values(cls, values: list[float]) -> StatisticalSummary:
        """从数值列表创建统计摘要"""
        if not values:
            return cls()

        summary = cls()
        summary.mean = statistics.mean(values)
        summary.median = statistics.median(values)
        summary.std_dev = statistics.stdev(values) if len(values) > 1 else 0.0
        summary.min_val = min(values)
        summary.max_val = max(values)

        # 计算百分位数
        sorted_values = sorted(values)
        n = len(sorted_values)
        summary.percentile_25 = sorted_values[int(n * 0.25)]
        summary.percentile_75 = sorted_values[int(n * 0.75)]
        summary.percentile_95 = sorted_values[int(n * 0.95)]

        # 判断趋势
        summary.trend = cls._detect_trend(values)

        return summary

    @staticmethod
    def _detect_trend(values: list[float], window: int = 10) -> str:
        """检测数据趋势"""
        if len(values) < window:
            return "stable"

        recent = values[-window:]
        earlier = (
            values[-2 * window : -window]
            if len(values) >= 2 * window
            else values[:window]
        )

        recent_mean = statistics.mean(recent)
        earlier_mean = statistics.mean(earlier)

        # 计算变化率
        if earlier_mean == 0:
            change_rate = 0.0
        else:
            change_rate = (recent_mean - earlier_mean) / earlier_mean

        # 计算波动性
        volatility = statistics.stdev(recent) / recent_mean if recent_mean > 0 else 0

        # 判断趋势
        if volatility > 0.5:
            return "volatile"
        if change_rate > 0.1:
            return "increasing"
        if change_rate < -0.1:
            return "decreasing"
        return "stable"


class TelemetryAggregator:
    """遥测数据聚合器 - 从原始遥测流中聚合性能指标"""

    def __init__(self, window_size_seconds: int = 60):
        self.window_size = timedelta(seconds=window_size_seconds)
        self.current_window_start = datetime.now(timezone.utc)

        # 当前窗口的累积数据
        self.executions = 0
        self.coverage_hits: set[int] = set()  # 唯一边缘集合
        self.crashes: list[dict[str, Any]] = []
        self.corpus_additions: list[dict[str, Any]] = []
        self.mutator_stats: defaultdict[str, dict[str, int]] = defaultdict(
            lambda: {"usage": 0, "success": 0},
        )

        # 实例ID (从遥测中提取)
        self.instance_id: str | None = None

        # 历史数据队列 (用于趋势分析)
        self.execution_history: deque[int] = deque(maxlen=100)
        self.coverage_history: deque[int] = deque(maxlen=100)
        self.crash_history: deque[int] = deque(maxlen=100)
        self.corpus_history: deque[int] = deque(maxlen=100)

        # 时间序列数据
        self.time_series: dict[str, deque[tuple[datetime, float]]] = {
            "execution_rate": deque(maxlen=1000),
            "path_discovery_rate": deque(maxlen=1000),
            "crash_rate": deque(maxlen=1000),
            "corpus_growth_rate": deque(maxlen=1000),
        }

    def process_telemetry_entry(self, entry: dict[str, Any]) -> None:
        """处理单个遥测条目"""
        entry_type = entry.get("type", "")

        # 提取实例ID (如果存在)
        if "instance_id" in entry and self.instance_id is None:
            self.instance_id = entry["instance_id"]

        if entry_type == "execution_count":
            self._process_execution_count(entry)
        elif entry_type == "coverage_hit":
            self._process_coverage_hit(entry)
        elif entry_type == "crash_found":
            self._process_crash_found(entry)
        elif entry_type == "corpus_grow":
            self._process_corpus_grow(entry)
        elif entry_type == "mutator_stats":
            self._process_mutator_stats(entry)

    def _process_execution_count(self, entry: dict[str, Any]) -> None:
        """处理执行计数条目"""
        count = entry.get("count", 0)
        self.executions += count

    def _process_coverage_hit(self, entry: dict[str, Any]) -> None:
        """处理覆盖率命中条目"""
        edge_id = entry.get("edge_id")
        if edge_id is not None:
            self.coverage_hits.add(edge_id)

    def _process_crash_found(self, entry: dict[str, Any]) -> None:
        """处理崩溃发现条目"""
        crash_info = {
            "type": entry.get("crash_type", "unknown"),
            "signal": entry.get("signal", 0),
            "hash": entry.get("input_hash", 0),
            "timestamp": datetime.now(timezone.utc),
        }
        self.crashes.append(crash_info)

    def _process_corpus_grow(self, entry: dict[str, Any]) -> None:
        """处理语料库增长条目"""
        corpus_info = {
            "new_inputs": entry.get("new_inputs", 0),
            "total_size": entry.get("total_size", 0),
            "avg_length": entry.get("avg_length", 0),
            "timestamp": datetime.now(timezone.utc),
        }
        self.corpus_additions.append(corpus_info)

    def _process_mutator_stats(self, entry: dict[str, Any]) -> None:
        """处理变异器统计条目"""
        mutator_id = entry.get("mutator_id", "unknown")
        usage_count = entry.get("usage_count", 0)
        success_rate = entry.get("success_rate", 0.0)

        stats = self.mutator_stats[mutator_id]
        stats["usage"] += usage_count
        stats["success"] = success_rate

    def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前窗口的性能指标"""
        current_time = datetime.now(timezone.utc)
        window_duration = (current_time - self.current_window_start).total_seconds()

        if window_duration == 0:
            window_duration = 1.0  # 避免除零

        metrics = PerformanceMetrics(
            total_executions=self.executions,
            execution_rate=self.executions / window_duration,
            coverage_hits=len(self.coverage_hits),
            unique_edges=len(self.coverage_hits),
            crashes_found=len(self.crashes),
            unique_crashes=len({c["hash"] for c in self.crashes}),
            window_start=self.current_window_start,
            window_end=current_time,
            instance_id=self.instance_id,  # 添加实例ID
        )

        # 计算崩溃率
        if self.executions > 0:
            metrics.crash_rate = (len(self.crashes) / self.executions) * 1_000_000

        # 计算语料库指标
        if self.corpus_additions:
            total_new = sum(c["new_inputs"] for c in self.corpus_additions)
            metrics.corpus_growth_rate = (
                total_new / window_duration
            ) * 60  # per minute

            latest_corpus = self.corpus_additions[-1]
            metrics.corpus_size = latest_corpus["total_size"]
            metrics.avg_input_length = latest_corpus["avg_length"]

        # 计算路径发现率
        new_paths = (
            len(self.coverage_hits) - sum(list(self.coverage_history)[-10:])
            if self.coverage_history
            else len(self.coverage_hits)
        )
        metrics.path_discovery_rate = (new_paths / window_duration) * 60  # per minute

        # 计算变异效率
        total_usage = sum(s["usage"] for s in self.mutator_stats.values())
        if total_usage > 0:
            weighted_success = sum(
                s["usage"] * s["success"] for s in self.mutator_stats.values()
            )
            metrics.mutation_efficiency = weighted_success / total_usage

        # 计算综合效率
        metrics.calculate_efficiency()

        return metrics

    def rotate_window(self) -> PerformanceMetrics:
        """轮转时间窗口，返回上一个窗口的指标"""
        # 获取当前窗口指标
        metrics = self.get_current_metrics()

        # 保存历史数据
        self.execution_history.append(self.executions)
        self.coverage_history.append(len(self.coverage_hits))
        self.crash_history.append(len(self.crashes))
        self.corpus_history.append(metrics.corpus_size)

        # 保存时间序列数据
        self.time_series["execution_rate"].append(
            (datetime.now(timezone.utc), metrics.execution_rate),
        )
        self.time_series["path_discovery_rate"].append(
            (datetime.now(timezone.utc), metrics.path_discovery_rate),
        )
        self.time_series["crash_rate"].append(
            (datetime.now(timezone.utc), metrics.crash_rate),
        )
        self.time_series["corpus_growth_rate"].append(
            (datetime.now(timezone.utc), metrics.corpus_growth_rate),
        )

        # 重置当前窗口
        self.current_window_start = datetime.now(timezone.utc)
        self.executions = 0
        self.coverage_hits.clear()
        self.crashes.clear()
        self.corpus_additions.clear()
        self.mutator_stats.clear()

        return metrics


class TelemetryAnalyzer:
    """遥测数据分析器 - 提供高级分析功能"""

    def __init__(self) -> None:
        self.aggregators: dict[
            str, TelemetryAggregator
        ] = {}  # instance_id -> aggregator
        self.metrics_history: list[PerformanceMetrics] = []
        self.anomaly_thresholds = {
            "execution_rate_drop": 0.5,  # 执行率下降50%
            "coverage_stagnation": 300,  # 300秒无新覆盖
            "crash_spike": 10.0,  # 崩溃率突增10倍
            "efficiency_drop": 0.3,  # 效率下降30%
        }

    async def analyze_telemetry_stream(
        self,
        telemetry_reader: Any,
        duration: timedelta,
        callback: Any | None = None,
    ) -> list[PerformanceMetrics]:
        """分析遥测数据流"""
        start_time = datetime.now(timezone.utc)
        end_time = start_time + duration

        # 窗口轮转间隔
        window_interval = timedelta(seconds=60)
        last_rotation = start_time

        while datetime.now(timezone.utc) < end_time:
            # 读取遥测数据批次
            try:
                entries = await telemetry_reader.read_batch(max_entries=1000)

                # 处理每个条目
                for entry in entries:
                    # 获取实例ID
                    instance_id = entry.get("instance_id", "default")

                    # 为新实例创建聚合器
                    if instance_id not in self.aggregators:
                        self.aggregators[instance_id] = TelemetryAggregator()

                    # 处理条目
                    self.aggregators[instance_id].process_telemetry_entry(entry)

            except Exception as e:
                logger.warning(f"Error reading telemetry: {e}")

            # 检查是否需要窗口轮转
            current_time = datetime.now(timezone.utc)
            if current_time - last_rotation >= window_interval:
                # 为每个实例轮转窗口
                for instance_id, aggregator in self.aggregators.items():
                    metrics = aggregator.rotate_window()
                    # 确保instance_id设置正确
                    metrics.instance_id = instance_id
                    self.metrics_history.append(metrics)

                    # 异常检测
                    anomalies = self.detect_anomalies(metrics)
                    if anomalies and callback:
                        await callback(metrics, anomalies)

                last_rotation = current_time

            # 短暂休眠避免CPU占用过高
            await asyncio.sleep(0.1)

        # 返回所有历史指标
        return self.metrics_history

    def detect_anomalies(self, metrics: PerformanceMetrics) -> list[str]:
        """检测性能异常"""
        anomalies = []

        # 检查执行率下降
        if self.metrics_history:
            recent_avg = statistics.mean(
                m.execution_rate for m in self.metrics_history[-5:]
            )
            if recent_avg > 0 and metrics.execution_rate < recent_avg * (
                1 - self.anomaly_thresholds["execution_rate_drop"]
            ):
                anomalies.append(
                    f"Execution rate dropped significantly: {metrics.execution_rate:.0f} exec/s (avg: {recent_avg:.0f})",
                )

        # 检查覆盖率停滞
        if len(self.metrics_history) > 5:
            recent_coverage = [m.unique_edges for m in self.metrics_history[-5:]]
            if all(c == recent_coverage[0] for c in recent_coverage):
                anomalies.append(
                    "Coverage stagnation detected: no new paths in 5 minutes",
                )

        # 检查崩溃率激增
        if self.metrics_history and metrics.crash_rate > 0:
            recent_crash_rates = [
                m.crash_rate for m in self.metrics_history[-5:] if m.crash_rate > 0
            ]
            if recent_crash_rates:
                avg_crash_rate = statistics.mean(recent_crash_rates)
                if (
                    metrics.crash_rate
                    > avg_crash_rate * self.anomaly_thresholds["crash_spike"]
                ):
                    anomalies.append(
                        f"Crash rate spike: {metrics.crash_rate:.1f} crashes/1M (avg: {avg_crash_rate:.1f})",
                    )

        # 检查效率下降
        if self.metrics_history:
            recent_efficiency = statistics.mean(
                m.efficiency_score for m in self.metrics_history[-5:]
            )
            if (
                recent_efficiency > 0
                and metrics.efficiency_score
                < recent_efficiency * (1 - self.anomaly_thresholds["efficiency_drop"])
            ):
                anomalies.append(
                    f"Efficiency dropped: {metrics.efficiency_score:.2f} (avg: {recent_efficiency:.2f})",
                )

        return anomalies

    def get_statistical_summary(self, metric_name: str) -> StatisticalSummary:
        """获取指定指标的统计摘要"""
        if not self.metrics_history:
            return StatisticalSummary()

        # 提取指定指标的值
        values = [
            getattr(m, metric_name)
            for m in self.metrics_history
            if hasattr(m, metric_name)
        ]

        return StatisticalSummary.from_values(values)

    def compare_performance(
        self,
        champion_metrics: list[PerformanceMetrics],
        shadow_metrics: list[PerformanceMetrics],
    ) -> dict[str, Any]:
        """比较两个fuzzer的性能"""
        if not champion_metrics or not shadow_metrics:
            return {"significant_difference": False, "winner": None}

        # 计算平均效率
        champion_efficiency = statistics.mean(
            m.efficiency_score for m in champion_metrics
        )
        shadow_efficiency = statistics.mean(m.efficiency_score for m in shadow_metrics)

        # 计算标准差
        champion_std = (
            statistics.stdev(m.efficiency_score for m in champion_metrics)
            if len(champion_metrics) > 1
            else 0
        )
        shadow_std = (
            statistics.stdev(m.efficiency_score for m in shadow_metrics)
            if len(shadow_metrics) > 1
            else 0
        )

        # 简单的显著性测试 (使用效应量)
        pooled_std = ((champion_std**2 + shadow_std**2) / 2) ** 0.5
        if pooled_std > 0:
            effect_size = abs(champion_efficiency - shadow_efficiency) / pooled_std
        else:
            effect_size = 0

        # 判断是否有显著差异 (Cohen's d > 0.5 为中等效应)
        significant_difference = effect_size > 0.5

        # 确定获胜者
        if significant_difference:
            winner = "shadow" if shadow_efficiency > champion_efficiency else "champion"
        else:
            winner = None

        return {
            "significant_difference": significant_difference,
            "winner": winner,
            "champion_efficiency": champion_efficiency,
            "shadow_efficiency": shadow_efficiency,
            "effect_size": effect_size,
            "improvement_percentage": (
                ((shadow_efficiency - champion_efficiency) / champion_efficiency * 100)
                if champion_efficiency > 0
                else 0
            ),
        }

    def generate_performance_report(self) -> dict[str, Any]:
        """生成性能分析报告"""
        if not self.metrics_history:
            return {"status": "no_data"}

        return {
            "summary": {
                "total_windows": len(self.metrics_history),
                "duration_minutes": len(self.metrics_history),
                "start_time": self.metrics_history[0].window_start.isoformat(),
                "end_time": self.metrics_history[-1].window_end.isoformat(),
            },
            "execution": self.get_statistical_summary("execution_rate"),
            "coverage": self.get_statistical_summary("unique_edges"),
            "crashes": self.get_statistical_summary("crashes_found"),
            "efficiency": self.get_statistical_summary("efficiency_score"),
            "trends": {
                "execution_trend": self._calculate_trend("execution_rate"),
                "coverage_trend": self._calculate_trend("unique_edges"),
                "efficiency_trend": self._calculate_trend("efficiency_score"),
            },
            "peak_performance": {
                "max_execution_rate": max(
                    m.execution_rate for m in self.metrics_history
                ),
                "max_path_discovery": max(
                    m.path_discovery_rate for m in self.metrics_history
                ),
                "max_efficiency": max(m.efficiency_score for m in self.metrics_history),
            },
        }

    def _calculate_trend(self, metric_name: str) -> str:
        """计算指标趋势"""
        if len(self.metrics_history) < 10:
            return "insufficient_data"

        values = [getattr(m, metric_name) for m in self.metrics_history]
        return StatisticalSummary._detect_trend(values)
